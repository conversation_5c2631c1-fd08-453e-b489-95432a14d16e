#!/usr/bin/env python3
"""
Visual Marking Showcase
Demonstrates browser-use's built-in visual marking system with Gemini
"""

import asyncio
import os
from browser_use import Agent
from browser_use.browser.session import BrowserSession
from langchain_google_genai import ChatGoogleGenerativeAI
from pydantic import SecretStr

async def showcase_visual_marking():
    """Showcase the visual marking system in action."""
    print("🎨 BROWSER-USE VISUAL MARKING SHOWCASE")
    print("="*50)
    print("This demo highlights browser-use's built-in visual marking")
    print("Watch how elements get highlighted and numbered!")
    print()
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No Gemini API key found!")
        return
    
    print(f"✅ Found API key: {api_key[:10]}...")
    
    try:
        # Setup Gemini
        print("\n🤖 Setting up Gemini AI...")
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            api_key=SecretStr(api_key),
            temperature=0.1
        )
        
        # Create browser with visual marking
        print("🌐 Creating browser with visual marking enabled...")
        browser = BrowserSession(
            headless=False,  # Must be False to see visual marking
            browser_config={
                'viewport': {'width': 1400, 'height': 900},
                'args': ['--start-maximized']
            }
        )
        
        print("\n🎯 SHOWCASE 1: Element Detection and Highlighting")
        print("-" * 50)
        print("Task: Navigate to a form page and observe element marking")
        
        task1 = """
        Navigate to httpbin.org/forms/post and examine the form.
        Don't fill it out yet - just look at all the form elements.
        Tell me what form fields you can see and how they are marked.
        """
        
        print("🔄 Starting element detection showcase...")
        print("👀 WATCH: Elements will be highlighted with colored borders and numbers!")
        
        agent1 = Agent(
            task=task1,
            llm=llm,
            browser=browser,
            use_vision=True
        )
        
        result1 = await agent1.run()
        print("✅ Element detection completed!")
        
        print("\n⏸️ Pausing 8 seconds to observe the visual marking...")
        await asyncio.sleep(8)
        
        print("\n🎯 SHOWCASE 2: Interactive Element Highlighting")
        print("-" * 50)
        print("Task: Navigate to a page with many interactive elements")
        
        task2 = """
        Navigate to httpbin.org (main page) and examine all the interactive elements.
        Look at the different sections, links, and buttons.
        Tell me about the different types of interactive elements you can see.
        """
        
        print("🔄 Starting interactive element showcase...")
        print("👀 WATCH: Different types of elements get different visual treatments!")
        
        agent2 = Agent(
            task=task2,
            llm=llm,
            browser=browser,
            use_vision=True
        )
        
        result2 = await agent2.run()
        print("✅ Interactive element showcase completed!")
        
        print("\n⏸️ Pausing 8 seconds to observe the marking...")
        await asyncio.sleep(8)
        
        print("\n🎯 SHOWCASE 3: Real-time Interaction with Visual Feedback")
        print("-" * 50)
        print("Task: Perform actual interactions and see real-time marking")
        
        task3 = """
        Go back to httpbin.org/forms/post and now interact with the form:
        1. Click on the 'Customer name' field and type 'Visual Demo'
        2. Click on the 'Email' field and type '<EMAIL>'
        3. Select 'Medium' for pizza size
        4. Don't submit - just show me how the visual marking helps with form interaction
        """
        
        print("🔄 Starting real-time interaction showcase...")
        print("👀 WATCH: Elements highlight as they're being interacted with!")
        
        agent3 = Agent(
            task=task3,
            llm=llm,
            browser=browser,
            use_vision=True
        )
        
        result3 = await agent3.run()
        print("✅ Real-time interaction showcase completed!")
        
        print("\n🎉 VISUAL MARKING SHOWCASE COMPLETE!")
        print("="*50)
        print("🌟 You've seen:")
        print("✅ Automatic element detection and highlighting")
        print("✅ Numbered elements for easy reference")
        print("✅ Different visual treatments for different element types")
        print("✅ Real-time highlighting during interactions")
        print("✅ Form field detection and interaction feedback")
        print()
        print("💡 The visual marking system:")
        print("   • Helps AI understand page structure")
        print("   • Provides visual feedback for debugging")
        print("   • Makes automation transparent and observable")
        print("   • Enables precise element targeting")
        
        print("\n⏸️ Keeping browser open for 15 seconds for final observation...")
        await asyncio.sleep(15)
        
    except Exception as e:
        print(f"❌ Showcase failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            if 'browser' in locals():
                await browser.close()
                print("✅ Browser closed")
        except:
            pass

async def simple_marking_demo():
    """Simple demo just to show marking in action."""
    print("🎨 SIMPLE VISUAL MARKING DEMO")
    print("="*40)
    
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No API key found")
        return
    
    try:
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            api_key=SecretStr(api_key)
        )
        
        browser = BrowserSession(headless=False)
        
        task = """
        Navigate to httpbin.org and just look around the page.
        Tell me what sections and interactive elements you can see.
        Focus on observing how elements are visually marked.
        """
        
        print("🎯 Simple task: Navigate and observe visual marking")
        print("👀 Watch for element highlighting and numbering!")
        
        agent = Agent(
            task=task,
            llm=llm,
            browser=browser,
            use_vision=True
        )
        
        result = await agent.run()
        
        print("✅ Simple demo completed!")
        print("⏸️ Browser will stay open for 10 seconds...")
        await asyncio.sleep(10)
        
        await browser.close()
        
    except Exception as e:
        print(f"❌ Simple demo failed: {e}")

def main():
    """Main function."""
    print("🎨 VISUAL MARKING DEMONSTRATION")
    print("="*40)
    print("See browser-use's visual marking system in action!")
    print()
    
    try:
        choice = input("""
Choose demonstration:
1. Simple Marking Demo (quick)
2. Full Visual Marking Showcase (comprehensive)
3. Exit

Enter choice (1-3): """).strip()
        
        if choice == "1":
            asyncio.run(simple_marking_demo())
        elif choice == "2":
            asyncio.run(showcase_visual_marking())
        elif choice == "3":
            print("👋 Goodbye!")
            return
        else:
            print("Invalid choice. Running simple demo...")
            asyncio.run(simple_marking_demo())
            
    except (EOFError, KeyboardInterrupt):
        print("\n👋 Demo cancelled by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    main()
