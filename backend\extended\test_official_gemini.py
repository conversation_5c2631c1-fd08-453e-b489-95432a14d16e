#!/usr/bin/env python3
"""
Test browser automation with official LangChain Gemini integration.
"""

import asyncio
import os
from langchain_google_genai import ChatGoogleGenerativeAI
from browser_use.agent.service import Agent
from browser_use.browser.session import BrowserSession

async def test_official_gemini():
    """Test with official LangChain Gemini integration."""
    print("🧪 Testing Official LangChain Gemini Integration")
    print("=" * 50)
    
    try:
        # Set API key
        api_key = "AIzaSyBEWog2PpUjLK64XW60niePRxtU1o8HWVM"
        os.environ["GOOGLE_API_KEY"] = api_key
        
        # Create official Gemini model
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            google_api_key=api_key,
            temperature=0
        )
        
        print(f"🤖 Created official Gemini model: {llm.model}")
        
        # Test simple generation first
        print("\n🧪 Testing simple text generation...")
        response = llm.invoke("Say hello in one word")
        print(f"✅ Simple test: {response.content}")
        
        # Create browser session
        print("\n🌐 Creating browser session...")
        browser_session = BrowserSession()
        
        # Create browser-use agent with official Gemini
        print("🤖 Creating browser-use agent...")
        agent = Agent(
            task="Go to https://example.com and tell me what you see",
            llm=llm,
            browser_session=browser_session
        )
        
        print("🚀 Running browser automation...")
        result = await agent.run()
        
        print(f"\n✅ Browser automation completed!")
        print(f"📄 Result: {result}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_official_gemini())
