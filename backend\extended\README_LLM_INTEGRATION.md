# SmartSurf LLM Integration Guide

This guide explains how to set up and use SmartSurf with different LLM providers: **Google Gemini** (recommended), **Ollama** (local), and **OpenAI**.

## 🚀 Quick Start (Recommended: Gemini)

For the fastest and most reliable experience, we recommend using Google Gemini:

1. **Get a free Gemini API key**: https://makersuite.google.com/app/apikey
2. **Set environment variable**:
   ```powershell
   # PowerShell
   $env:GEMINI_API_KEY='your-api-key-here'
   
   # Or alternatively
   $env:GOOGLE_API_KEY='your-api-key-here'
   ```
3. **Use SmartSurf**:
   ```python
   from browser_use_agent import BrowserUseAgent
   agent = BrowserUseAgent()  # Auto-detects Gemini
   result = await agent.execute_task("Go to google.com and search for Python")
   ```

## 🤖 Google Gemini Integration (Recommended)

### Why Gemini?
- ✅ **Fast and reliable** - No local setup required
- ✅ **Excellent reasoning** - Great for complex browser tasks
- ✅ **Free tier available** - Generous usage limits
- ✅ **No CUDA issues** - Works on any machine

### Setup Steps

1. **Get API Key**:
   - Visit: https://makersuite.google.com/app/apikey
   - Click "Create API Key"
   - Copy your key

2. **Install Dependencies** (already done):
   ```bash
   pip install google-generativeai
   ```

3. **Set Environment Variable**:
   ```powershell
   # PowerShell (Windows)
   $env:GEMINI_API_KEY='your-api-key-here'
   
   # CMD (Windows)
   set GEMINI_API_KEY=your-api-key-here
   
   # Bash (Linux/Mac)
   export GEMINI_API_KEY='your-api-key-here'
   ```

4. **Test the Integration**:
   ```bash
   python test_gemini_integration.py
   python example_gemini_usage.py
   ```

### Usage Examples

```python
# Basic usage (auto-detects Gemini if API key is set)
agent = BrowserUseAgent()

# Specific Gemini model
agent = BrowserUseAgent(gemini_model="gemini-1.5-pro")

# Custom Gemini instance
from browser_use_agent import GeminiChatModel
custom_llm = GeminiChatModel(model="gemini-1.5-flash", api_key="your-key")
agent = BrowserUseAgent(llm=custom_llm)
```

## 🦙 Ollama Integration (Local)

### Why Ollama?
- ✅ **Privacy** - Everything runs locally
- ✅ **No API costs** - Free to use
- ✅ **Offline capable** - Works without internet
- ⚠️ **Requires setup** - More complex installation

### Setup Steps

1. **Install Ollama**:
   - Download from: https://ollama.ai/download
   - Install and start the service

2. **Pull a Model**:
   ```bash
   ollama pull llama3.2
   ollama pull llama2
   ```

3. **Start Ollama Service**:
   ```bash
   ollama serve
   ```

4. **Test the Integration**:
   ```bash
   python test_ollama_only.py
   ```

### Common Ollama Issues

**CUDA Errors**: If you get CUDA memory errors, try CPU-only mode:
```bash
# Stop Ollama
taskkill /f /im ollama.exe

# Start in CPU-only mode
$env:OLLAMA_NUM_GPU="0"
ollama serve
```

**Model Loading Issues**: Re-download models:
```bash
ollama pull llama3.2:latest
ollama pull llama2:latest
```

### Usage Examples

```python
# Specific Ollama model
agent = BrowserUseAgent(ollama_model="llama2")

# Custom Ollama instance
from browser_use_agent import OllamaChatModel
custom_llm = OllamaChatModel(model="llama3.2", base_url="http://localhost:11434")
agent = BrowserUseAgent(llm=custom_llm)
```

## 🔗 OpenAI Integration

### Setup Steps

1. **Get API Key**: https://platform.openai.com/api-keys
2. **Set Environment Variables**:
   ```powershell
   $env:OPENAI_API_KEY='your-api-key-here'
   $env:USE_OPENAI='true'
   ```
3. **Install Dependencies**:
   ```bash
   pip install langchain-openai
   ```

## 🔄 Model Priority

SmartSurf automatically selects the best available model in this order:

1. **Gemini** (if `GEMINI_API_KEY` or `GOOGLE_API_KEY` is set)
2. **OpenAI** (if `OPENAI_API_KEY` and `USE_OPENAI=true`)
3. **Ollama** (fallback, if running locally)

## 🧪 Testing

Run these test scripts to verify your setup:

```bash
# Test Gemini integration
python test_gemini_integration.py

# Test Ollama integration
python test_ollama_only.py

# Test browser automation
python test_browser_automation.py

# See usage examples
python example_gemini_usage.py
python example_ollama_usage.py
```

## 🚀 Browser Automation Examples

```python
import asyncio
from browser_use_agent import BrowserUseAgent

async def main():
    # Create agent (auto-selects best available model)
    agent = BrowserUseAgent()
    
    # Simple navigation task
    result = await agent.execute_task(
        "Go to https://httpbin.org/get and tell me what you see"
    )
    
    # Complex task
    result = await agent.execute_task(
        "Navigate to Wikipedia, search for 'Python programming', and summarize the first paragraph"
    )
    
    print(f"Success: {result.success}")
    print(f"Result: {result.result}")

asyncio.run(main())
```

## 🔧 Troubleshooting

### Gemini Issues
- **API Key Error**: Make sure your API key is valid and set correctly
- **Quota Exceeded**: Check your usage at https://makersuite.google.com/

### Ollama Issues
- **Connection Error**: Make sure `ollama serve` is running
- **CUDA Errors**: Use CPU-only mode with `OLLAMA_NUM_GPU=0`
- **Model Not Found**: Pull the model with `ollama pull model-name`

### Browser-use Issues
- **Import Error**: Install with `pip install browser-use`
- **Browser Not Found**: Make sure Chrome/Chromium is installed

## 📚 Additional Resources

- **Gemini API Documentation**: https://ai.google.dev/docs
- **Ollama Documentation**: https://ollama.ai/docs
- **Browser-use Documentation**: https://docs.browser-use.com/

## 🎯 Recommendations

- **For Production**: Use Gemini for reliability and speed
- **For Development**: Use Gemini for fast iteration
- **For Privacy**: Use Ollama for sensitive data
- **For Cost**: Use Gemini free tier, then Ollama
