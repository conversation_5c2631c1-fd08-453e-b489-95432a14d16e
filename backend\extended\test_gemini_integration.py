#!/usr/bin/env python3
"""
Test Gemini integration with browser automation.
"""

import asyncio
import os
from browser_use_agent import <PERSON><PERSON>er<PERSON>seAgent, GeminiChatModel

def test_gemini_setup():
    """Test Gemini setup and basic functionality."""
    print("🤖 Testing Gemini Integration")
    print("=" * 40)
    
    # Check for API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No Gemini API key found!")
        print("💡 To set up Gemini:")
        print("1. Go to https://makersuite.google.com/app/apikey")
        print("2. Create a new API key")
        print("3. Set environment variable:")
        print("   $env:GEMINI_API_KEY='your-api-key-here'")
        print("   or")
        print("   $env:GOOGLE_API_KEY='your-api-key-here'")
        return False
    
    try:
        print(f"✅ Found API key: {api_key[:10]}...")
        
        # Test Gemini model creation
        print("\n🧪 Testing Gemini model creation...")
        model = GeminiChatModel(api_key=api_key)
        print(f"✅ Created GeminiChatModel: {model.model}")
        
        # Test simple text generation
        print("\n📝 Testing text generation...")
        from langchain_core.messages import HumanMessage
        messages = [HumanMessage(content="Say 'Hello from Gemini!' and nothing else.")]
        result = model._generate(messages)
        response = result.generations[0].message.content
        
        print(f"📤 Sent: Say 'Hello from Gemini!' and nothing else.")
        print(f"📥 Received: {response}")
        
        if "Hello" in response or "Gemini" in response:
            print("✅ Gemini text generation is working!")
            return True
        else:
            print("⚠️  Gemini responded but content seems unexpected")
            return True
            
    except Exception as e:
        print(f"❌ Error testing Gemini: {e}")
        return False

async def test_gemini_browser_automation():
    """Test browser automation with Gemini."""
    print("\n🌐 Testing Browser Automation with Gemini")
    print("=" * 50)
    
    try:
        # Create agent with Gemini (will auto-detect if API key is available)
        print("🤖 Creating BrowserUseAgent with Gemini...")
        agent = BrowserUseAgent()  # Will auto-select Gemini if API key is available
        print(f"✅ Agent created with model: {agent.llm.model}")
        
        # Simple test task
        task = "Navigate to wikipedia and search imran khan and tell me what you see"
        print(f"\n📋 Task: {task}")
        print("🔄 Executing task... (this may take a moment)")
        
        # Execute the task
        result = await agent.execute_task(task, headless=True)
        
        # Display results
        if result.success:
            print("\n✅ Task completed successfully!")
            print(f"📄 Result: {result.result}")
        else:
            print(f"\n❌ Task failed: {result.error}")
            
    except Exception as e:
        print(f"\n❌ Error during browser automation: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Run all tests."""
    print("🚀 SmartSurf Gemini Integration Test")
    print("=" * 50)
    
    # Test basic Gemini setup
    if not test_gemini_setup():
        print("\n💡 Set up your Gemini API key and try again!")
        return
    
    # Test browser automation
    print("\n❓ Would you like to test browser automation with Gemini?")
    try:
        choice = input("Test browser automation? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            asyncio.run(test_gemini_browser_automation())
        else:
            print("Skipping browser automation test.")
    except (EOFError, KeyboardInterrupt):
        print("Skipping browser automation test.")
    
    print("\n🎉 Gemini integration test complete!")
    print("\n💡 Next steps:")
    print("1. Set GEMINI_API_KEY environment variable")
    print("2. Use BrowserUseAgent() - it will auto-select Gemini")
    print("3. Enjoy fast, reliable browser automation!")

if __name__ == "__main__":
    main()
