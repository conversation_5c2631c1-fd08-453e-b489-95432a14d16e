#!/usr/bin/env python3
"""
Setup script for Ollama integration with SmartSurf Browser Use Agent

This script helps you set up Ollama and download recommended models for browser automation.
"""

import subprocess
import sys
import requests
import time
import json
from typing import List, Dict

def check_ollama_installed() -> bool:
    """Check if Ollama is installed on the system."""
    try:
        result = subprocess.run(['ollama', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Ollama is installed: {result.stdout.strip()}")
            return True
        else:
            print("❌ Ollama is not installed")
            return False
    except FileNotFoundError:
        print("❌ Ollama is not installed")
        return False

def check_ollama_running() -> bool:
    """Check if Ollama service is running."""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            print("✅ Ollama service is running")
            return True
        else:
            print("❌ Ollama service is not responding")
            return False
    except requests.exceptions.RequestException:
        print("❌ Ollama service is not running")
        return False

def start_ollama_service():
    """Start the Ollama service."""
    print("Starting Ollama service...")
    try:
        # Try to start Ollama in the background
        subprocess.Popen(['ollama', 'serve'], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        time.sleep(3)  # Give it time to start
        
        if check_ollama_running():
            print("✅ Ollama service started successfully")
            return True
        else:
            print("❌ Failed to start Ollama service")
            return False
    except Exception as e:
        print(f"❌ Error starting Ollama service: {e}")
        return False

def get_available_models() -> List[Dict]:
    """Get list of available models from Ollama."""
    try:
        response = requests.get("http://localhost:11434/api/tags")
        if response.status_code == 200:
            return response.json().get("models", [])
        else:
            return []
    except requests.exceptions.RequestException:
        return []

def pull_model(model_name: str) -> bool:
    """Pull a model from Ollama registry."""
    print(f"Downloading {model_name}... This may take a while.")
    try:
        result = subprocess.run(['ollama', 'pull', model_name], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Successfully downloaded {model_name}")
            return True
        else:
            print(f"❌ Failed to download {model_name}: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error downloading {model_name}: {e}")
        return False

def setup_recommended_models():
    """Setup recommended models for browser automation."""
    recommended_models = [
        "llama3.2",  # Good general purpose model
        "codellama",  # Good for code-related tasks
        "llama3.1",   # Alternative general purpose model
    ]
    
    available_models = get_available_models()
    available_model_names = [model['name'].split(':')[0] for model in available_models]
    
    print(f"Currently installed models: {available_model_names}")
    
    for model in recommended_models:
        if model not in available_model_names:
            print(f"\n📥 {model} is not installed.")
            choice = input(f"Would you like to download {model}? (y/n): ").lower().strip()
            if choice in ['y', 'yes']:
                pull_model(model)
        else:
            print(f"✅ {model} is already installed")

def create_env_file():
    """Create a .env file with Ollama configuration."""
    env_content = """# Ollama Configuration for SmartSurf Browser Use Agent
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2

# Set to true if you want to use OpenAI instead of Ollama
USE_OPENAI=false
# OPENAI_API_KEY=your_openai_api_key_here
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("✅ Created .env file with Ollama configuration")
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")

def main():
    """Main setup function."""
    print("🚀 SmartSurf Ollama Setup")
    print("=" * 40)
    
    # Check if Ollama is installed
    if not check_ollama_installed():
        print("\n📋 Please install Ollama first:")
        print("   Visit: https://ollama.ai/download")
        print("   Or use: curl -fsSL https://ollama.ai/install.sh | sh")
        return
    
    # Check if Ollama is running
    if not check_ollama_running():
        print("\n🔄 Ollama service is not running. Attempting to start...")
        if not start_ollama_service():
            print("\n📋 Please start Ollama manually:")
            print("   Run: ollama serve")
            return
    
    # Setup recommended models
    print("\n📦 Setting up recommended models...")
    setup_recommended_models()
    
    # Create environment file
    print("\n⚙️  Creating configuration...")
    create_env_file()
    
    print("\n🎉 Setup complete!")
    print("\n📋 Next steps:")
    print("1. Make sure Ollama is running: ollama serve")
    print("2. Test the browser agent: python browser_use_agent.py")
    print("3. Customize models in .env file if needed")

if __name__ == "__main__":
    main()
