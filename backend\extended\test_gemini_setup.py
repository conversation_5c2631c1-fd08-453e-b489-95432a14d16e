#!/usr/bin/env python3
"""
Test Gemini Setup and Integration
Validates that all components are working correctly
"""

import asyncio
import os
import sys
from typing import List, <PERSON><PERSON>

def check_environment() -> List[Tuple[str, bool, str]]:
    """Check environment setup."""
    checks = []
    
    # Check Python version
    python_version = sys.version_info
    python_ok = python_version >= (3, 8)
    checks.append(("Python 3.8+", python_ok, f"Found Python {python_version.major}.{python_version.minor}"))
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    api_ok = bool(api_key)
    checks.append(("Gemini API Key", api_ok, f"Found: {api_key[:10]}..." if api_key else "Not found"))
    
    # Check imports
    try:
        import google.generativeai as genai
        gemini_ok = True
        gemini_msg = "✅ Available"
    except ImportError:
        gemini_ok = False
        gemini_msg = "❌ Not installed - run: pip install google-generativeai"
    checks.append(("Google Generative AI", gemini_ok, gemini_msg))
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        langchain_ok = True
        langchain_msg = "✅ Available"
    except ImportError:
        langchain_ok = False
        langchain_msg = "❌ Not installed - run: pip install langchain-google-genai"
    checks.append(("LangChain Google GenAI", langchain_ok, langchain_msg))
    
    try:
        from browser_use import Agent
        from browser_use.browser.session import BrowserSession
        browser_use_ok = True
        browser_use_msg = "✅ Available"
    except ImportError:
        browser_use_ok = False
        browser_use_msg = "❌ Not installed - run: pip install browser-use"
    checks.append(("Browser Use", browser_use_ok, browser_use_msg))
    
    return checks

async def test_gemini_api():
    """Test Gemini API connection."""
    print("\n🧪 Testing Gemini API Connection...")
    
    try:
        import google.generativeai as genai
        
        api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ No API key found")
            return False
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel("gemini-2.0-flash-exp")
        
        response = model.generate_content("Say 'Hello from Gemini!' and nothing else.")
        
        print(f"✅ API Response: {response.text}")
        return True
        
    except Exception as e:
        print(f"❌ API Test Failed: {e}")
        return False

async def test_browser_use():
    """Test browser-use library."""
    print("\n🌐 Testing Browser-Use Library...")
    
    try:
        from browser_use.browser.session import BrowserSession
        
        print("Creating browser session...")
        browser = BrowserSession(headless=True)
        
        print("✅ Browser session created successfully")
        return True
        
    except Exception as e:
        print(f"❌ Browser-Use Test Failed: {e}")
        return False

async def test_integration():
    """Test the complete integration."""
    print("\n🔗 Testing Complete Integration...")
    
    try:
        from gemini_browser_integration import GeminiBrowserAgent
        
        print("Creating Gemini Browser Agent...")
        agent = GeminiBrowserAgent(headless=True, debug=False)
        
        print("Testing simple task...")
        result = await agent.execute_task(
            "Navigate to httpbin.org and tell me the title of the page",
            url="https://httpbin.org"
        )
        
        if result.success:
            print(f"✅ Integration Test Passed!")
            print(f"📄 Result: {result.result}")
            return True
        else:
            print(f"❌ Integration Test Failed: {result.error}")
            return False
            
    except Exception as e:
        print(f"❌ Integration Test Failed: {e}")
        return False

async def run_quick_demo():
    """Run a quick demo to show it's working."""
    print("\n🚀 Running Quick Demo...")
    
    try:
        from gemini_browser_integration import GeminiBrowserAgent
        
        agent = GeminiBrowserAgent(headless=False, debug=True)
        
        task = "Go to httpbin.org, look around the page, and tell me what this website is for and what services it provides."
        
        print(f"🎯 Demo Task: {task}")
        print("🔄 Executing... (browser window will open)")
        
        result = await agent.execute_task(task)
        
        if result.success:
            print("\n✅ DEMO COMPLETED SUCCESSFULLY!")
            print("="*50)
            print("📄 RESULT:")
            print(result.result)
            print(f"\n⏱️ Execution time: {result.execution_time:.2f} seconds")
            return True
        else:
            print(f"\n❌ Demo failed: {result.error}")
            return False
            
    except Exception as e:
        print(f"❌ Demo crashed: {e}")
        return False

def print_setup_instructions():
    """Print setup instructions."""
    print("\n📋 SETUP INSTRUCTIONS")
    print("="*50)
    print("1. Install required packages:")
    print("   pip install google-generativeai langchain-google-genai browser-use")
    print()
    print("2. Get a Gemini API key:")
    print("   - Go to https://makersuite.google.com/app/apikey")
    print("   - Create a new API key")
    print()
    print("3. Set environment variable:")
    print("   # PowerShell")
    print("   $env:GEMINI_API_KEY='your-api-key-here'")
    print("   # or")
    print("   $env:GOOGLE_API_KEY='your-api-key-here'")
    print()
    print("4. Run the test again:")
    print("   python test_gemini_setup.py")

async def main():
    """Main test function."""
    print("🧪 GEMINI BROWSER INTEGRATION - SETUP TEST")
    print("="*60)
    
    # Environment checks
    print("🔍 Checking Environment...")
    checks = check_environment()
    
    all_good = True
    for name, status, message in checks:
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {name}: {message}")
        if not status:
            all_good = False
    
    if not all_good:
        print("\n❌ Environment check failed!")
        print_setup_instructions()
        return
    
    print("\n✅ Environment check passed!")
    
    # API test
    api_ok = await test_gemini_api()
    if not api_ok:
        print("\n❌ API test failed!")
        return
    
    # Browser test
    browser_ok = await test_browser_use()
    if not browser_ok:
        print("\n❌ Browser test failed!")
        return
    
    # Integration test
    integration_ok = await test_integration()
    if not integration_ok:
        print("\n❌ Integration test failed!")
        return
    
    print("\n🎉 ALL TESTS PASSED!")
    print("="*60)
    print("✅ Gemini API: Working")
    print("✅ Browser-Use: Working") 
    print("✅ Integration: Working")
    
    # Ask if user wants to see a demo
    try:
        choice = input("\n🎬 Would you like to see a quick demo? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            demo_ok = await run_quick_demo()
            if demo_ok:
                print("\n🎉 SETUP COMPLETE AND WORKING!")
                print("You can now run:")
                print("- python gemini_navigation_demo.py")
                print("- python gemini_demo_comprehensive.py")
            else:
                print("\n⚠️ Demo failed, but basic setup is working")
        else:
            print("\n✅ Setup test complete!")
            print("You can now run the demo scripts.")
    except (EOFError, KeyboardInterrupt):
        print("\n✅ Setup test complete!")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test cancelled by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
