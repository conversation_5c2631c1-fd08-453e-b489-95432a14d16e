#!/usr/bin/env python3
"""
ACTUALLY WORKING browser demo that will show you the visual marking!
"""

import asyncio
from browser_use.browser.session import BrowserSession

async def working_browser_demo():
    """Actually working browser demo."""
    print("🚀 ACTUALLY WORKING Browser Demo")
    print("=" * 40)
    
    browser = None
    try:
        print("🌐 Creating browser session...")
        browser = BrowserSession(headless=False)
        
        print("🔧 Starting browser...")
        await browser.start()
        
        print("📍 Navigating to form page...")
        await browser.navigate_to("https://httpbin.org/forms/post")
        
        print("\n🎯 SUCCESS! Browser window should be open!")
        print("👀 You should see:")
        print("   • Browser window with the form page")
        print("   • Form fields visible")
        print("   • You can interact with the page")
        
        print("\n⏳ Keeping browser open for 30 seconds...")
        print("   Try clicking on form elements!")
        
        await asyncio.sleep(30)
        
        print("\n✅ Demo complete!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if browser:
            try:
                await browser.stop()
            except:
                pass

if __name__ == "__main__":
    print("🎯 ACTUALLY WORKING Browser Visual Demo")
    print("=" * 50)
    print("This WILL open a browser window!")
    print("Press Enter to start...")
    
    try:
        input()
    except:
        pass
    
    asyncio.run(working_browser_demo())
