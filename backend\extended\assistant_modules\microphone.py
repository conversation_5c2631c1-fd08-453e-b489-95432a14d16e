"""Microphone handling for voice assistant."""

import asyncio
import threading
import queue
import pyaudio
import numpy as np
from typing import Optional


class AsyncMicrophone:
    """Asynchronous microphone handler for voice input."""
    
    def __init__(self, sample_rate: int = 16000, chunk_size: int = 1024):
        self.sample_rate = sample_rate
        self.chunk_size = chunk_size
        self.audio_queue = queue.Queue()
        self.is_recording = False
        self.is_receiving = False
        self.audio_thread: Optional[threading.Thread] = None
        self.pyaudio_instance = None
        self.stream = None
        
    def start_recording(self):
        """Start recording audio from microphone."""
        if self.is_recording:
            return
            
        self.is_recording = True
        self.pyaudio_instance = pyaudio.PyAudio()
        
        self.stream = self.pyaudio_instance.open(
            format=pyaudio.paInt16,
            channels=1,
            rate=self.sample_rate,
            input=True,
            frames_per_buffer=self.chunk_size,
            stream_callback=self._audio_callback
        )
        
        self.stream.start_stream()
        
    def stop_recording(self):
        """Stop recording audio."""
        self.is_recording = False
        if self.stream:
            self.stream.stop_stream()
            self.stream.close()
        if self.pyaudio_instance:
            self.pyaudio_instance.terminate()
            
    def close(self):
        """Close the microphone."""
        self.stop_recording()
        
    def _audio_callback(self, in_data, frame_count, time_info, status):
        """Callback for audio stream."""
        if self.is_recording:
            self.audio_queue.put(in_data)
        return (None, pyaudio.paContinue)
        
    def get_audio_data(self) -> Optional[bytes]:
        """Get audio data from the queue."""
        try:
            return self.audio_queue.get_nowait()
        except queue.Empty:
            return None
