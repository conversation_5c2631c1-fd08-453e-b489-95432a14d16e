#!/usr/bin/env python3
"""
Test browser-use visual element marking and debugging features.
"""

import asyncio
import os
from browser_use_agent import BrowserUseAgent

async def test_visual_marking():
    """Test browser automation with visual element marking enabled."""
    print("🎯 Testing Browser-use Visual Element Marking")
    print("=" * 50)
    
    try:
        # Create agent (will use best available model)
        agent = BrowserUseAgent()
        print(f"🤖 Using model: {agent.llm.model}")
        
        # Test with visual debugging enabled
        task = """
        Navigate to https://httpbin.org/forms/post and:
        1. Take a screenshot showing all form elements
        2. Identify and highlight the input fields
        3. Show me what elements are available for interaction
        """
        
        print(f"\n📋 Task: {task}")
        print("🔄 Executing with visual marking enabled...")
        print("👀 Browser will show element highlighting and annotations")
        
        # Execute task with headless=False to see visual marking
        result = await agent.execute_task(task, headless=False)
        
        if result.success:
            print("\n✅ Task completed successfully!")
            print(f"📄 Result: {result.result}")
        else:
            print(f"\n❌ Task failed: {result.error}")
            
    except Exception as e:
        print(f"\n❌ Error: {e}")

async def test_screenshot_mode():
    """Test with screenshot debugging mode."""
    print("\n📸 Testing Screenshot Debug Mode")
    print("=" * 40)
    
    try:
        agent = BrowserUseAgent()
        
        # Simple task that will generate annotated screenshots
        task = "Go to https://example.com and take a screenshot showing all clickable elements"
        
        print(f"📋 Task: {task}")
        print("📸 This will save annotated screenshots to debug folder")
        
        result = await agent.execute_task(task, headless=False)
        
        if result.success:
            print("✅ Screenshots saved with element annotations!")
            print("📁 Check the browser-use debug folder for annotated images")
        
    except Exception as e:
        print(f"❌ Error: {e}")

def show_visual_features():
    """Show available visual marking features."""
    print("\n🎨 Browser-use Visual Features")
    print("=" * 40)
    
    features = [
        "🎯 Element Highlighting - Highlights interactive elements",
        "🔢 Element Numbering - Shows numbered markers on elements", 
        "📦 Bounding Boxes - Displays element boundaries",
        "📸 Screenshot Annotations - Saves marked screenshots",
        "🔍 Debug Mode - Shows detailed element information",
        "🖱️ Click Visualization - Shows where clicks happen",
        "⌨️ Input Highlighting - Highlights form fields",
        "🔗 Link Detection - Identifies all clickable links"
    ]
    
    for feature in features:
        print(f"  {feature}")
    
    print("\n⚙️ Configuration Options:")
    print("  • headless=False - Shows browser window with visual marking")
    print("  • debug=True - Enables detailed debugging output")
    print("  • save_screenshots=True - Saves annotated screenshots")

def main():
    """Run visual marking tests."""
    print("🚀 Browser-use Visual Element Marking Test")
    print("=" * 50)
    
    # Show available features
    show_visual_features()
    
    print("\n❓ Would you like to test visual element marking?")
    print("(This will open a browser window showing element highlighting)")
    
    try:
        choice = input("Test visual marking? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            print("\n🔄 Starting visual marking test...")
            print("👀 Watch the browser window for element highlighting!")
            asyncio.run(test_visual_marking())
            
            print("\n❓ Test screenshot mode too?")
            choice2 = input("Test screenshots? (y/n): ").lower().strip()
            if choice2 in ['y', 'yes']:
                asyncio.run(test_screenshot_mode())
        else:
            print("Skipping visual tests.")
            
    except (EOFError, KeyboardInterrupt):
        print("Skipping visual tests.")
    
    print("\n💡 Visual Marking Tips:")
    print("1. Use headless=False to see element highlighting")
    print("2. Browser-use automatically marks interactive elements")
    print("3. Screenshots are saved with element annotations")
    print("4. Debug mode provides detailed element information")
    print("5. Element IDs help the AI understand page structure")

if __name__ == "__main__":
    main()
