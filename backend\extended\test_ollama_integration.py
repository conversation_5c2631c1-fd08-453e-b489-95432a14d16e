#!/usr/bin/env python3
"""
Test script for Ollama integration with SmartSurf Browser Use Agent

This script tests the Ollama integration without requiring browser automation.
"""

import asyncio
import sys
import os
from browser_use_agent import OllamaChatModel, BrowserUseAgent
from langchain_core.messages import HumanMessage

async def test_ollama_model():
    """Test the Ollama model directly."""
    print("🧪 Testing Ollama Model Integration")
    print("=" * 50)
    
    try:
        # Test basic Ollama connection
        model = OllamaChatModel(model="llama3.2")
        print(f"✅ Created Ollama model: {model.model}")
        print(f"📡 Base URL: {model.base_url}")
        
        # Test a simple generation
        print("\n🔄 Testing model generation...")
        messages = [HumanMessage(content="Hello! Please respond with just 'Hello, SmartSurf!' and nothing else.")]
        
        result = model._generate(messages)
        response = result.generations[0].message.content
        
        print(f"📤 Sent: Hello! Please respond with just 'Hello, SmartSurf!' and nothing else.")
        print(f"📥 Received: {response}")
        
        if "SmartSurf" in response or "Hello" in response:
            print("✅ Model is responding correctly!")
            return True
        else:
            print("⚠️  Model responded but content seems unexpected")
            return True
            
    except Exception as e:
        print(f"❌ Error testing Ollama model: {e}")
        return False

async def test_browser_agent_init():
    """Test BrowserUseAgent initialization."""
    print("\n🤖 Testing BrowserUseAgent Initialization")
    print("=" * 50)
    
    try:
        # Test agent creation with default Ollama
        agent = BrowserUseAgent()
        print("✅ Created BrowserUseAgent with default Ollama model")
        
        # Test agent creation with specific model
        agent_custom = BrowserUseAgent(ollama_model="llama3.2")
        print("✅ Created BrowserUseAgent with custom Ollama model")
        
        # Test LLM type
        print(f"📋 LLM type: {agent.llm._llm_type}")
        print(f"📋 Model name: {agent.llm.model}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing BrowserUseAgent: {e}")
        return False

def test_ollama_connection():
    """Test basic Ollama connection."""
    print("🔌 Testing Ollama Connection")
    print("=" * 50)
    
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        
        if response.status_code == 200:
            models = response.json().get("models", [])
            print("✅ Ollama is running and accessible")
            print(f"📦 Available models: {[m['name'] for m in models]}")
            
            # Check if recommended models are available
            model_names = [m['name'].split(':')[0] for m in models]
            recommended = ["llama3.2", "llama3.1", "codellama"]
            
            for model in recommended:
                if model in model_names:
                    print(f"✅ {model} is available")
                else:
                    print(f"⚠️  {model} is not installed (run: ollama pull {model})")
            
            return True
        else:
            print(f"❌ Ollama responded with status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        print("💡 Make sure Ollama is running: ollama serve")
        return False

async def run_simple_task_test():
    """Test a simple browser task (if browser-use is available)."""
    print("\n🌐 Testing Simple Browser Task")
    print("=" * 50)
    
    try:
        agent = BrowserUseAgent()
        
        # Simple task that doesn't require complex web interaction
        task = "Navigate to https://httpbin.org/get and return the URL from the response"
        
        print(f"📋 Task: {task}")
        print("🔄 Executing task... (this may take a moment)")
        
        result = await agent.execute_task(task, headless=True)
        
        if result.success:
            print("✅ Task completed successfully!")
            print(f"📄 Result: {result.result}")
            return True
        else:
            print(f"❌ Task failed: {result.error}")
            return False
            
    except Exception as e:
        print(f"❌ Error during browser task: {e}")
        print("💡 This might be due to missing browser-use dependencies")
        return False

async def main():
    """Run all tests."""
    print("🚀 SmartSurf Ollama Integration Test Suite")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 0
    
    # Test 1: Ollama connection
    total_tests += 1
    if test_ollama_connection():
        tests_passed += 1
    
    # Test 2: Ollama model
    total_tests += 1
    if await test_ollama_model():
        tests_passed += 1
    
    # Test 3: Browser agent initialization
    total_tests += 1
    if await test_browser_agent_init():
        tests_passed += 1
    
    # Test 4: Simple browser task (optional)
    print("\n❓ Would you like to test a simple browser automation task?")
    choice = input("This requires browser-use dependencies (y/n): ").lower().strip()
    
    if choice in ['y', 'yes']:
        total_tests += 1
        if await run_simple_task_test():
            tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Ollama integration is working correctly.")
    elif tests_passed > 0:
        print("⚠️  Some tests passed. Check the failures above.")
    else:
        print("❌ All tests failed. Please check your Ollama setup.")
    
    print("\n💡 Tips:")
    print("- Make sure Ollama is running: ollama serve")
    print("- Install recommended models: ollama pull llama3.2")
    print("- Check the setup script: python setup_ollama.py")

if __name__ == "__main__":
    asyncio.run(main())
