#!/usr/bin/env python3
"""
Gemini End-to-End Navigation Demo
Simple demonstration of Gemini-powered browser navigation with visual marking
"""

import asyncio
import os
from gemini_browser_integration import GeminiBrowserAgent

async def demo_wikipedia_navigation():
    """Demo: Navigate Wikipedia and extract information."""
    print("🌐 GEMINI WIKIPEDIA NAVIGATION DEMO")
    print("="*50)
    
    try:
        # Create agent with visual browser (headless=False)
        agent = GeminiBrowserAgent(
            model="gemini-2.0-flash-exp",
            headless=False,  # Show browser for visual demonstration
            debug=True
        )
        
        print("🎯 Task: Navigate Wikipedia and search for information")
        
        task = """
        Navigate to Wikipedia and perform the following steps:
        1. Go to the main Wikipedia page
        2. Search for 'Machine Learning'
        3. Click on the Machine Learning article
        4. Read the introduction and extract key points about what machine learning is
        5. Find and click on a related topic link (like 'Artificial Intelligence' or 'Deep Learning')
        6. Provide a summary of what you learned from both pages
        
        Be descriptive about each step you take and what you observe.
        """
        
        print("🔄 Starting navigation...")
        result = await agent.execute_task(task, url="https://wikipedia.org")
        
        if result.success:
            print("\n✅ NAVIGATION COMPLETED SUCCESSFULLY!")
            print("="*50)
            print("📄 RESULT:")
            print(result.result)
            print(f"\n⏱️ Execution time: {result.execution_time:.2f} seconds")
        else:
            print("\n❌ NAVIGATION FAILED!")
            print("="*50)
            print(f"🚫 Error: {result.error}")
            print(f"⏱️ Execution time: {result.execution_time:.2f} seconds")
            
    except Exception as e:
        print(f"❌ Demo failed with exception: {e}")

async def demo_form_filling():
    """Demo: Fill out a form with Gemini."""
    print("\n🌐 GEMINI FORM FILLING DEMO")
    print("="*50)
    
    try:
        agent = GeminiBrowserAgent(headless=False, debug=True)
        
        print("🎯 Task: Fill out a test form")
        
        task = """
        Navigate to the httpbin.org forms page and fill out the form:
        1. Go to httpbin.org/forms/post
        2. Fill in the customer name field with 'Gemini Test User'
        3. Fill in the telephone field with '555-0123'
        4. Fill in the email field with '<EMAIL>'
        5. Select 'Large' for the size
        6. Select 'pepperoni' for the topping
        7. Select 'now' for delivery time
        8. Add a comment: 'This form was filled by Gemini AI'
        9. Submit the form
        10. Tell me what response you get after submission
        
        Describe each step as you perform it.
        """
        
        print("🔄 Starting form filling...")
        result = await agent.execute_task(task)
        
        if result.success:
            print("\n✅ FORM FILLING COMPLETED!")
            print("="*50)
            print("📄 RESULT:")
            print(result.result)
            print(f"\n⏱️ Execution time: {result.execution_time:.2f} seconds")
        else:
            print("\n❌ FORM FILLING FAILED!")
            print("="*50)
            print(f"🚫 Error: {result.error}")
            print(f"⏱️ Execution time: {result.execution_time:.2f} seconds")
            
    except Exception as e:
        print(f"❌ Demo failed with exception: {e}")

async def demo_search_and_analyze():
    """Demo: Search and analyze results."""
    print("\n🌐 GEMINI SEARCH AND ANALYSIS DEMO")
    print("="*50)
    
    try:
        agent = GeminiBrowserAgent(headless=False, debug=True)
        
        print("🎯 Task: Search and analyze information")
        
        task = """
        Perform a comprehensive search and analysis:
        1. Go to Google
        2. Search for 'browser automation tools 2024'
        3. Look at the top 3-5 search results
        4. Click on at least 2 different results to read more details
        5. Analyze and compare the different browser automation tools mentioned
        6. Provide a summary of:
           - What browser automation tools are popular
           - Their key features and differences
           - Which ones seem most suitable for different use cases
        
        Take your time to read and analyze the content thoroughly.
        """
        
        print("🔄 Starting search and analysis...")
        result = await agent.execute_task(task, url="https://google.com")
        
        if result.success:
            print("\n✅ SEARCH AND ANALYSIS COMPLETED!")
            print("="*50)
            print("📄 RESULT:")
            print(result.result)
            print(f"\n⏱️ Execution time: {result.execution_time:.2f} seconds")
        else:
            print("\n❌ SEARCH AND ANALYSIS FAILED!")
            print("="*50)
            print(f"🚫 Error: {result.error}")
            print(f"⏱️ Execution time: {result.execution_time:.2f} seconds")
            
    except Exception as e:
        print(f"❌ Demo failed with exception: {e}")

async def run_all_demos():
    """Run all navigation demos in sequence."""
    print("🚀 GEMINI BROWSER AUTOMATION - END-TO-END DEMOS")
    print("="*60)
    print("This will run 3 comprehensive demos showing Gemini's browser automation capabilities")
    print("Each demo will open a visible browser window so you can see the automation in action")
    print()
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No Gemini API key found!")
        print("💡 Please set your API key:")
        print("   $env:GEMINI_API_KEY='your-api-key-here'")
        return
    
    print(f"✅ Found API key: {api_key[:10]}...")
    print()
    
    demos = [
        ("Wikipedia Navigation", demo_wikipedia_navigation),
        ("Form Filling", demo_form_filling),
        ("Search and Analysis", demo_search_and_analyze)
    ]
    
    for i, (name, demo_func) in enumerate(demos, 1):
        print(f"\n🎬 STARTING DEMO {i}/3: {name}")
        print("-" * 60)
        
        try:
            await demo_func()
            print(f"✅ Demo {i} completed successfully!")
        except Exception as e:
            print(f"❌ Demo {i} failed: {e}")
        
        if i < len(demos):
            print("\n⏸️ Pausing 5 seconds before next demo...")
            await asyncio.sleep(5)
    
    print("\n🎉 ALL DEMOS COMPLETED!")
    print("="*60)
    print("You've seen Gemini AI perform:")
    print("✅ Intelligent web navigation")
    print("✅ Form filling and interaction")
    print("✅ Search and content analysis")
    print("✅ Multi-step complex workflows")
    print("\nThis demonstrates the power of combining Gemini AI with browser automation!")

def main():
    """Main function."""
    print("🤖 Gemini Browser Navigation Demo")
    print("="*40)
    
    try:
        choice = input("""
Choose a demo:
1. Wikipedia Navigation Demo
2. Form Filling Demo  
3. Search and Analysis Demo
4. Run All Demos
5. Quick Test

Enter choice (1-5): """).strip()
        
        if choice == "1":
            asyncio.run(demo_wikipedia_navigation())
        elif choice == "2":
            asyncio.run(demo_form_filling())
        elif choice == "3":
            asyncio.run(demo_search_and_analyze())
        elif choice == "4":
            asyncio.run(run_all_demos())
        elif choice == "5":
            # Quick test
            async def quick_test():
                agent = GeminiBrowserAgent(headless=False)
                result = await agent.execute_task("Go to httpbin.org and tell me what you see")
                print(f"Result: {result.result if result.success else result.error}")
            asyncio.run(quick_test())
        else:
            print("Invalid choice. Running Wikipedia demo...")
            asyncio.run(demo_wikipedia_navigation())
            
    except (EOFError, KeyboardInterrupt):
        print("\n👋 Demo cancelled by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    main()
