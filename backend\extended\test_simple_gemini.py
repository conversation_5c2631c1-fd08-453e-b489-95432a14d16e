import google.generativeai as genai

api_key = "AIzaSyCTfajQ30R8_zwddc3ftEq3YwUklxdtges"
genai.configure(api_key=api_key)

# Test with the simplest model
try:
    model = genai.GenerativeModel('gemini-1.5-flash')
    response = model.generate_content("Say hello")
    print(f"✅ Success: {response.text}")
except Exception as e:
    print(f"❌ Error: {e}")

# Try with different model names
models_to_try = ['gemini-1.5-flash', 'gemini-pro', 'gemini-1.5-pro']
for model_name in models_to_try:
    try:
        print(f"\nTrying {model_name}...")
        model = genai.GenerativeModel(model_name)
        response = model.generate_content("Hello")
        print(f"✅ {model_name}: {response.text[:50]}...")
        break
    except Exception as e:
        print(f"❌ {model_name}: {e}")
