#!/usr/bin/env python3
"""
Gemini End-to-End Navigation Demo with Built-in Visual Marking
Uses browser-use's built-in visual marking system with Gemini AI
"""

import asyncio
import os
import time
from browser_use import Agent
from browser_use.browser.session import BrowserSession
from langchain_google_genai import ChatGoogleGenerative<PERSON>I
from pydantic import SecretStr

async def gemini_end_to_end_demo():
    """Complete end-to-end demo with Gemini and visual marking."""
    print("🚀 GEMINI END-TO-END NAVIGATION WITH VISUAL MARKING")
    print("="*70)
    print("This demo shows Gemini AI controlling browser automation")
    print("with browser-use's built-in visual element marking system")
    print()
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No Gemini API key found!")
        print("💡 Please set your API key:")
        print("   $env:GEMINI_API_KEY='your-api-key-here'")
        return
    
    print(f"✅ Found API key: {api_key[:10]}...")
    
    try:
        # Create Gemini LLM
        print("\n🤖 Setting up Gemini AI...")
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            api_key=SecretStr(api_key),
            temperature=0.1
        )
        print("✅ Gemini AI configured")
        
        # Create browser session with visual marking enabled
        print("\n🌐 Creating browser session with visual marking...")
        browser = BrowserSession(
            headless=False,  # Visual mode to see the marking
            browser_config={
                'viewport': {'width': 1280, 'height': 720},
                'args': ['--start-maximized']
            }
        )
        print("✅ Browser session created")
        
        # Demo 1: Simple Navigation with Visual Marking
        print("\n📍 DEMO 1: Navigation with Visual Element Detection")
        print("-" * 60)
        
        task1 = """
        Navigate to httpbin.org and explore the page. 
        Look at the different sections and tell me what services this website offers.
        Take your time to examine the visual elements on the page.
        """
        
        print(f"🎯 Task: Navigate and analyze httpbin.org")
        print("🔄 Starting browser automation...")
        print("👀 Watch the browser window - you'll see elements being marked!")
        
        agent1 = Agent(
            task=task1,
            llm=llm,
            browser=browser,
            use_vision=True,  # Enable vision for better element detection
            save_conversation_path="./demo1_conversation.json"
        )
        
        start_time = time.time()
        result1 = await agent1.run()
        end_time = time.time()
        
        print(f"\n✅ Demo 1 completed in {end_time - start_time:.2f} seconds")
        print("📄 Result:")
        print("-" * 40)
        if hasattr(result1, 'final_result'):
            print(result1.final_result())
        else:
            print(str(result1))
        print("-" * 40)
        
        print("\n⏸️ Pausing 5 seconds before next demo...")
        await asyncio.sleep(5)
        
        # Demo 2: Form Interaction with Visual Marking
        print("\n📝 DEMO 2: Form Interaction with Visual Marking")
        print("-" * 60)
        
        task2 = """
        Navigate to httpbin.org/forms/post and fill out the form completely:
        
        1. Fill 'Customer name' with: John Doe
        2. Fill 'Telephone' with: 555-1234
        3. Fill 'Email address' with: <EMAIL>
        4. Select 'Large' for pizza size
        5. Select 'cheese' for topping
        6. Select 'now' for delivery time
        7. Add comment: 'Test order from Gemini AI'
        8. Submit the form
        9. Tell me what response you get
        
        Pay attention to the visual marking of form elements as you interact with them.
        """
        
        print(f"🎯 Task: Complete form filling with visual feedback")
        print("🔄 Starting form automation...")
        print("👀 Watch how elements get highlighted as they're identified!")
        
        agent2 = Agent(
            task=task2,
            llm=llm,
            browser=browser,
            use_vision=True,
            save_conversation_path="./demo2_conversation.json"
        )
        
        start_time = time.time()
        result2 = await agent2.run()
        end_time = time.time()
        
        print(f"\n✅ Demo 2 completed in {end_time - start_time:.2f} seconds")
        print("📄 Result:")
        print("-" * 40)
        if hasattr(result2, 'final_result'):
            print(result2.final_result())
        else:
            print(str(result2))
        print("-" * 40)
        
        print("\n⏸️ Pausing 5 seconds before next demo...")
        await asyncio.sleep(5)
        
        # Demo 3: Multi-step Navigation with Search
        print("\n🔍 DEMO 3: Multi-step Navigation and Search")
        print("-" * 60)
        
        task3 = """
        Perform a comprehensive web search and analysis:
        
        1. Go to Google (google.com)
        2. Search for "browser automation tools 2024"
        3. Look at the search results and click on the first relevant result
        4. Read the content and identify key browser automation tools mentioned
        5. Go back to Google and search for "playwright vs selenium"
        6. Click on a comparison article
        7. Provide a summary of the differences between these tools
        
        Throughout this process, observe how the visual marking helps identify clickable elements.
        """
        
        print(f"🎯 Task: Multi-step search and analysis")
        print("🔄 Starting complex navigation...")
        print("👀 Watch the visual marking across different websites!")
        
        agent3 = Agent(
            task=task3,
            llm=llm,
            browser=browser,
            use_vision=True,
            max_actions_per_step=3,  # Allow more actions per step for complex tasks
            save_conversation_path="./demo3_conversation.json"
        )
        
        start_time = time.time()
        result3 = await agent3.run()
        end_time = time.time()
        
        print(f"\n✅ Demo 3 completed in {end_time - start_time:.2f} seconds")
        print("📄 Result:")
        print("-" * 40)
        if hasattr(result3, 'final_result'):
            print(result3.final_result())
        else:
            print(str(result3))
        print("-" * 40)
        
        # Final Summary
        print("\n🎉 END-TO-END DEMO COMPLETED!")
        print("="*70)
        print("🌟 What you've seen:")
        print("✅ Gemini AI making intelligent decisions about web navigation")
        print("✅ Browser-use's built-in visual marking highlighting interactive elements")
        print("✅ Real-time element detection and interaction")
        print("✅ Form filling with visual feedback")
        print("✅ Multi-step workflows across different websites")
        print("✅ Search and content analysis capabilities")
        print()
        print("💡 The visual marking system helps Gemini:")
        print("   • Identify clickable elements with colored borders")
        print("   • Understand page structure and layout")
        print("   • Make precise interactions with form fields")
        print("   • Navigate complex multi-step workflows")
        print()
        print("🔗 Conversation logs saved:")
        print("   • demo1_conversation.json - Navigation demo")
        print("   • demo2_conversation.json - Form interaction demo")
        print("   • demo3_conversation.json - Multi-step search demo")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Keep browser open for a moment to see final state
        print("\n⏸️ Keeping browser open for 10 seconds to observe final state...")
        await asyncio.sleep(10)
        
        try:
            if 'browser' in locals():
                await browser.close()
                print("✅ Browser closed")
        except:
            pass

async def quick_visual_test():
    """Quick test to show visual marking in action."""
    print("🧪 QUICK VISUAL MARKING TEST")
    print("="*40)
    
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No API key found")
        return
    
    try:
        llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            api_key=SecretStr(api_key),
            temperature=0.1
        )
        
        browser = BrowserSession(headless=False)
        
        task = """
        Go to httpbin.org and click on the 'HTTP Methods' section.
        Then tell me what you see on that page.
        Pay attention to how elements are visually marked.
        """
        
        print("🎯 Quick task: Navigate and interact with visual marking")
        print("👀 Watch for element highlighting!")
        
        agent = Agent(
            task=task,
            llm=llm,
            browser=browser,
            use_vision=True
        )
        
        result = await agent.run()
        
        print("✅ Quick test completed!")
        if hasattr(result, 'final_result'):
            print(f"📄 Result: {result.final_result()}")
        
        await asyncio.sleep(5)
        await browser.close()
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")

def main():
    """Main function."""
    print("🤖 GEMINI + BROWSER-USE VISUAL MARKING DEMO")
    print("="*60)
    print("Experience Gemini AI with browser-use's built-in visual marking!")
    print()
    
    try:
        choice = input("""
Choose demo:
1. Quick Visual Test (5 minutes)
2. Full End-to-End Demo (15-20 minutes)
3. Exit

Enter choice (1-3): """).strip()
        
        if choice == "1":
            asyncio.run(quick_visual_test())
        elif choice == "2":
            asyncio.run(gemini_end_to_end_demo())
        elif choice == "3":
            print("👋 Goodbye!")
            return
        else:
            print("Invalid choice. Running quick test...")
            asyncio.run(quick_visual_test())
            
    except (EOFError, KeyboardInterrupt):
        print("\n👋 Demo cancelled by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    main()
