#!/usr/bin/env python3
"""
Example usage of SmartSurf with Google Gemini integration.

This script demonstrates how to use Gemini for browser automation tasks.
"""

import asyncio
import os
from browser_use_agent import BrowserUseAgent, GeminiChatModel

def example_gemini_setup():
    """Example of setting up Gemini."""
    print("🤖 Gemini Setup Example")
    print("=" * 30)
    
    print("1. Get your Gemini API key:")
    print("   🔗 https://makersuite.google.com/app/apikey")
    print()
    print("2. Set environment variable (choose one):")
    print("   PowerShell: $env:GEMINI_API_KEY='your-api-key-here'")
    print("   CMD:        set GEMINI_API_KEY=your-api-key-here")
    print("   Or:         $env:GOOGLE_API_KEY='your-api-key-here'")
    print()
    print("3. Use in your code:")
    print("   from browser_use_agent import BrowserUseAgent")
    print("   agent = BrowserUseAgent()  # Auto-detects Gemini")

def example_direct_gemini():
    """Example of using GeminiChatModel directly."""
    print("\n🤖 Direct Gemini Usage Example")
    print("=" * 40)
    
    # Check for API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No API key found. Please set GEMINI_API_KEY first.")
        return False
    
    try:
        # Create a Gemini model
        model = GeminiChatModel(api_key=api_key)
        print(f"✅ Created model: {model.model}")
        
        # Example 1: Simple text generation
        print("\n📝 Example 1: Simple text generation")
        from langchain_core.messages import HumanMessage
        messages = [HumanMessage(content="Write a short greeting message.")]
        result = model._generate(messages)
        response = result.generations[0].message.content
        print(f"Response: {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def example_browser_agent_with_gemini():
    """Example of initializing BrowserUseAgent with Gemini."""
    print("\n🌐 Browser Agent with Gemini Example")
    print("=" * 40)
    
    # Check for API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No API key found. Please set GEMINI_API_KEY first.")
        return
    
    try:
        # Create agent (will auto-detect Gemini if API key is available)
        agent1 = BrowserUseAgent()
        print(f"✅ Created agent with auto-detected model: {agent1.llm.model}")
        
        # Create agent with specific Gemini model
        agent2 = BrowserUseAgent(gemini_model="gemini-1.5-pro")
        print(f"✅ Created agent with gemini-1.5-pro: {agent2.llm.model}")
        
        # Create agent with custom Gemini instance
        custom_llm = GeminiChatModel(model="gemini-1.5-flash", api_key=api_key)
        agent3 = BrowserUseAgent(llm=custom_llm)
        print(f"✅ Created agent with custom LLM: {agent3.llm.model}")
        
    except Exception as e:
        print(f"❌ Error: {e}")

async def example_browser_task_with_gemini():
    """Example of a browser automation task with Gemini."""
    print("\n🔧 Browser Automation with Gemini Example")
    print("=" * 50)
    
    # Check for API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No API key found. Please set GEMINI_API_KEY first.")
        return
    
    try:
        # Create agent with Gemini
        agent = BrowserUseAgent()  # Will auto-select Gemini
        print(f"🤖 Using model: {agent.llm.model}")
        
        # Simple task
        task = "Navigate to https://httpbin.org/get and tell me what you see"
        print(f"📋 Task: {task}")
        
        # Execute task
        result = await agent.execute_task(task, headless=True)
        
        if result.success:
            print("✅ Task completed successfully!")
            print(f"📄 Result: {result.result}")
        else:
            print(f"❌ Task failed: {result.error}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run all examples."""
    print("🚀 SmartSurf Gemini Usage Examples")
    print("=" * 50)
    
    # Example 1: Setup instructions
    example_gemini_setup()
    
    # Check if API key is available
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("\n⚠️  No Gemini API key found!")
        print("Please set up your API key first and run this script again.")
        return
    
    # Example 2: Direct Gemini usage
    if not example_direct_gemini():
        return
    
    # Example 3: Browser agent initialization
    example_browser_agent_with_gemini()
    
    # Example 4: Browser automation (optional)
    print("\n❓ Would you like to test browser automation with Gemini?")
    print("(This will open a browser and navigate to a test page)")
    
    try:
        choice = input("Test browser automation? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            asyncio.run(example_browser_task_with_gemini())
        else:
            print("Skipping browser automation example.")
    except (EOFError, KeyboardInterrupt):
        print("Skipping browser automation example.")
    
    print("\n🎉 Examples complete!")
    print("\n💡 Key advantages of Gemini:")
    print("✅ Fast and reliable responses")
    print("✅ No local setup required")
    print("✅ Excellent reasoning capabilities")
    print("✅ Free tier available")
    print("\n💡 Next steps:")
    print("1. Use BrowserUseAgent() for automatic model selection")
    print("2. Gemini will be prioritized over Ollama if API key is set")
    print("3. Enjoy fast, reliable browser automation!")

if __name__ == "__main__":
    main()
