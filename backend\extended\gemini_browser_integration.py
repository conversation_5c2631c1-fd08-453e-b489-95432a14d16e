#!/usr/bin/env python3
"""
Enhanced Gemini Browser Integration for WebRover
Combines Google Gemini AI with browser-use library for intelligent web automation.
"""

import asyncio
import os
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from dotenv import load_dotenv

# Gemini imports
try:
    import google.generativeai as genai
    from langchain_google_genai import ChatGoogleGenerativeAI
    from pydantic import SecretStr
    GEMINI_AVAILABLE = True
except ImportError:
    print("❌ Gemini not available. Install with: pip install google-generativeai langchain-google-genai")
    GEMINI_AVAILABLE = False

# Browser-use imports
try:
    from browser_use import Agent
    from browser_use.browser.session import BrowserSession
    BROWSER_USE_AVAILABLE = True
except ImportError:
    print("❌ browser-use not available. Install with: pip install browser-use")
    BROWSER_USE_AVAILABLE = False

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class BrowserTaskResult:
    """Result container for browser automation tasks."""
    success: bool
    result: Any
    error: Optional[str] = None
    screenshots: List[str] = None
    execution_time: float = 0.0

class GeminiBrowserAgent:
    """
    Enhanced Gemini-powered browser automation agent.
    
    Features:
    - Google Gemini 2.0 Flash for intelligent decision making
    - Visual element detection and interaction
    - Screenshot analysis and navigation
    - Error handling and recovery
    - Detailed logging and debugging
    """
    
    def __init__(
        self, 
        model: str = "gemini-2.0-flash-exp",
        api_key: Optional[str] = None,
        headless: bool = False,
        debug: bool = True
    ):
        """
        Initialize the Gemini Browser Agent.
        
        Args:
            model: Gemini model to use
            api_key: Gemini API key (if not provided, will use environment variable)
            headless: Whether to run browser in headless mode
            debug: Enable debug logging
        """
        load_dotenv()
        
        if not GEMINI_AVAILABLE:
            raise ImportError("Gemini not available. Install with: pip install google-generativeai langchain-google-genai")
        
        if not BROWSER_USE_AVAILABLE:
            raise ImportError("browser-use not available. Install with: pip install browser-use")
        
        # Setup Gemini
        self.api_key = api_key or os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("Gemini API key required. Set GEMINI_API_KEY environment variable or pass api_key parameter")
        
        self.model = model
        self.headless = headless
        self.debug = debug
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        
        # Create LangChain Gemini model for browser-use compatibility
        self.llm = ChatGoogleGenerativeAI(
            model=self.model,
            api_key=SecretStr(self.api_key),
            temperature=0.1  # Low temperature for consistent behavior
        )
        
        logger.info(f"🤖 Initialized Gemini Browser Agent with model: {self.model}")
    
    async def execute_task(
        self, 
        task: str,
        url: Optional[str] = None,
        max_steps: int = 10,
        save_screenshots: bool = True
    ) -> BrowserTaskResult:
        """
        Execute a browser automation task using Gemini AI.
        
        Args:
            task: Natural language description of the task
            url: Optional starting URL
            max_steps: Maximum number of automation steps
            save_screenshots: Whether to save screenshots during execution
            
        Returns:
            BrowserTaskResult with execution details
        """
        import time
        start_time = time.time()
        
        try:
            logger.info(f"🚀 Starting task: {task}")
            if url:
                logger.info(f"🌐 Starting URL: {url}")
            
            # Create browser session
            browser_session = BrowserSession(headless=self.headless)
            
            # Enhanced task prompt for better results
            enhanced_task = self._enhance_task_prompt(task, url)
            
            # Create and run agent
            agent = Agent(
                task=enhanced_task,
                llm=self.llm,
                browser=browser_session,
                max_actions_per_step=3,  # Limit actions per step for better control
                use_vision=True  # Enable vision for better element detection
            )
            
            logger.info("🔄 Executing browser automation...")
            result = await agent.run()
            
            # Extract final result
            if hasattr(result, 'final_result'):
                final_result = result.final_result()
            elif hasattr(result, 'result'):
                final_result = result.result
            else:
                final_result = str(result)
            
            execution_time = time.time() - start_time
            logger.info(f"✅ Task completed successfully in {execution_time:.2f}s")
            
            return BrowserTaskResult(
                success=True,
                result=final_result,
                execution_time=execution_time
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Browser automation failed: {str(e)}"
            logger.error(f"❌ {error_msg}")
            
            return BrowserTaskResult(
                success=False,
                result=None,
                error=error_msg,
                execution_time=execution_time
            )
    
    def _enhance_task_prompt(self, task: str, url: Optional[str] = None) -> str:
        """Enhance the task prompt for better Gemini performance."""
        enhanced_prompt = f"""
You are an expert web automation assistant. Execute this task step by step:

TASK: {task}

INSTRUCTIONS:
1. Be methodical and careful with each action
2. Wait for pages to load completely before taking actions
3. Use descriptive element selection (prefer text content over generic selectors)
4. If you encounter errors, try alternative approaches
5. Provide clear descriptions of what you're doing and seeing
6. Take screenshots when helpful for verification

"""
        
        if url:
            enhanced_prompt += f"\nSTARTING URL: {url}\n"
        
        enhanced_prompt += """
IMPORTANT: Focus on accuracy over speed. Describe each step clearly.
"""
        
        return enhanced_prompt
    
    async def navigate_and_analyze(self, url: str) -> BrowserTaskResult:
        """Navigate to a URL and analyze the page content."""
        task = f"Navigate to {url} and provide a detailed analysis of the page content, including main sections, navigation elements, and key information."
        return await self.execute_task(task, url)
    
    async def search_and_extract(self, search_query: str, search_engine: str = "google.com") -> BrowserTaskResult:
        """Perform a search and extract relevant information."""
        task = f"Go to {search_engine}, search for '{search_query}', and extract the most relevant information from the top results."
        return await self.execute_task(task, f"https://{search_engine}")
    
    async def form_interaction(self, url: str, form_data: Dict[str, str]) -> BrowserTaskResult:
        """Navigate to a page and fill out a form with provided data."""
        form_instructions = "\n".join([f"- Fill '{field}' with '{value}'" for field, value in form_data.items()])
        task = f"Navigate to the page and fill out the form with this data:\n{form_instructions}\nThen submit the form if appropriate."
        return await self.execute_task(task, url)

# Convenience functions for easy usage
async def gemini_browse(task: str, **kwargs) -> BrowserTaskResult:
    """Quick function to execute a browser task with Gemini."""
    agent = GeminiBrowserAgent(**kwargs)
    return await agent.execute_task(task)

async def gemini_search(query: str, **kwargs) -> BrowserTaskResult:
    """Quick function to perform a search with Gemini."""
    agent = GeminiBrowserAgent(**kwargs)
    return await agent.search_and_extract(query)

async def gemini_analyze_page(url: str, **kwargs) -> BrowserTaskResult:
    """Quick function to analyze a webpage with Gemini."""
    agent = GeminiBrowserAgent(**kwargs)
    return await agent.navigate_and_analyze(url)

# Example usage
async def example_usage():
    """Example of how to use the Gemini Browser Agent."""
    print("🚀 Gemini Browser Integration Example")
    print("=" * 50)
    
    try:
        # Create agent
        agent = GeminiBrowserAgent(headless=False, debug=True)
        
        # Example 1: Simple navigation and analysis
        print("\n📍 Example 1: Analyzing a webpage")
        result = await agent.navigate_and_analyze("https://httpbin.org/forms/post")
        
        if result.success:
            print(f"✅ Success: {result.result}")
        else:
            print(f"❌ Error: {result.error}")
        
        # Example 2: Search task
        print("\n🔍 Example 2: Performing a search")
        result = await agent.search_and_extract("Python programming tutorials")
        
        if result.success:
            print(f"✅ Search results: {result.result}")
        else:
            print(f"❌ Search error: {result.error}")
            
    except Exception as e:
        print(f"❌ Example failed: {e}")

if __name__ == "__main__":
    asyncio.run(example_usage())
