#!/usr/bin/env python3
"""
Test browser automation with Ollama integration.
"""

import asyncio
from browser_use_agent import BrowserUseAgent

async def test_browser_automation():
    """Test browser automation with Ollama."""
    print("🌐 Testing Browser Automation with Ollama")
    print("=" * 50)
    
    try:
        # Create agent with Ollama
        print("🦙 Creating BrowserUseAgent with Ollama...")
        agent = BrowserUseAgent(ollama_model="llama2")
        print(f"✅ Agent created with model: {agent.llm.model}")
        
        # Simple test task
        task = "Navigate to wikipedia and search <PERSON><PERSON> <PERSON>han and tell me what you see"
        print(f"\n📋 Task: {task}")
        print("🔄 Executing task... (this may take a moment)")
        
        # Execute the task
        result = await agent.execute_task(task, headless=True)
        
        # Display results
        if result.success:
            print("\n✅ Task completed successfully!")
            print(f"📄 Result: {result.result}")
        else:
            print(f"\n❌ Task failed: {result.error}")
            
    except Exception as e:
        print(f"\n❌ Error during browser automation: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_browser_automation())
