#!/usr/bin/env python3
"""
Comprehensive Gemini Browser Automation Demo
Showcases end-to-end browser automation using Google Gemini AI
"""

import asyncio
import os
import time
from typing import List, Dict
from gemini_browser_integration import GeminiBrowserAgent, BrowserTaskResult

class GeminiDemoSuite:
    """Comprehensive demo suite for Gemini browser automation."""
    
    def __init__(self, headless: bool = False):
        self.headless = headless
        self.agent = None
        self.results: List[Dict] = []
    
    async def setup(self):
        """Setup the Gemini agent."""
        print("🚀 Setting up Gemini Browser Agent...")
        try:
            self.agent = GeminiBrowserAgent(
                model="gemini-2.0-flash-exp",
                headless=self.headless,
                debug=True
            )
            print("✅ Gemini agent initialized successfully!")
            return True
        except Exception as e:
            print(f"❌ Failed to setup agent: {e}")
            return False
    
    async def demo_1_basic_navigation(self):
        """Demo 1: Basic navigation and page analysis."""
        print("\n" + "="*60)
        print("📍 DEMO 1: Basic Navigation and Page Analysis")
        print("="*60)
        
        task = "Navigate to httpbin.org and analyze the main page content. Tell me what services and endpoints are available."
        
        print(f"🎯 Task: {task}")
        print("🔄 Executing...")
        
        result = await self.agent.execute_task(task, url="https://httpbin.org")
        
        self._log_result("Basic Navigation", result)
        return result.success
    
    async def demo_2_form_interaction(self):
        """Demo 2: Form filling and interaction."""
        print("\n" + "="*60)
        print("📝 DEMO 2: Form Interaction")
        print("="*60)
        
        task = """Navigate to httpbin.org/forms/post and fill out the form with the following information:
        - Customer name: John Doe
        - Telephone: 555-1234
        - Email: <EMAIL>
        - Size: Medium
        - Topping: cheese
        - Delivery time: now
        - Comments: This is a test order
        
        After filling the form, submit it and tell me what response you get."""
        
        print(f"🎯 Task: Form filling and submission")
        print("🔄 Executing...")
        
        result = await self.agent.execute_task(task, url="https://httpbin.org/forms/post")
        
        self._log_result("Form Interaction", result)
        return result.success
    
    async def demo_3_search_and_extract(self):
        """Demo 3: Search functionality and information extraction."""
        print("\n" + "="*60)
        print("🔍 DEMO 3: Search and Information Extraction")
        print("="*60)
        
        task = """Go to Wikipedia, search for 'Artificial Intelligence', and extract the following information:
        1. The main definition of AI from the article
        2. Key historical milestones mentioned
        3. Current applications listed
        4. Any notable researchers or pioneers mentioned
        
        Provide a structured summary of this information."""
        
        print(f"🎯 Task: Wikipedia search and extraction")
        print("🔄 Executing...")
        
        result = await self.agent.execute_task(task, url="https://wikipedia.org")
        
        self._log_result("Search and Extract", result)
        return result.success
    
    async def demo_4_ecommerce_simulation(self):
        """Demo 4: E-commerce browsing simulation."""
        print("\n" + "="*60)
        print("🛒 DEMO 4: E-commerce Browsing Simulation")
        print("="*60)
        
        task = """Navigate to a demo e-commerce site and perform the following actions:
        1. Browse the product categories
        2. Search for a specific product (laptop or electronics)
        3. View product details for at least one item
        4. Add an item to cart (if possible)
        5. Provide a summary of the shopping experience and product information found
        
        Note: This is just for demonstration - do not complete any actual purchases."""
        
        print(f"🎯 Task: E-commerce browsing simulation")
        print("🔄 Executing...")
        
        # Using a demo e-commerce site
        result = await self.agent.execute_task(task, url="https://demo.opencart.com")
        
        self._log_result("E-commerce Simulation", result)
        return result.success
    
    async def demo_5_complex_navigation(self):
        """Demo 5: Complex multi-step navigation."""
        print("\n" + "="*60)
        print("🗺️ DEMO 5: Complex Multi-step Navigation")
        print("="*60)
        
        task = """Perform a complex navigation task:
        1. Go to GitHub.com
        2. Search for 'browser automation' repositories
        3. Find a popular repository related to browser automation
        4. Navigate to its README file
        5. Extract key information about the project (description, features, installation)
        6. Check the repository's issues section
        7. Provide a comprehensive report about the project
        
        Focus on accuracy and provide detailed observations at each step."""
        
        print(f"🎯 Task: Complex GitHub navigation")
        print("🔄 Executing...")
        
        result = await self.agent.execute_task(task, url="https://github.com")
        
        self._log_result("Complex Navigation", result)
        return result.success
    
    def _log_result(self, demo_name: str, result: BrowserTaskResult):
        """Log the result of a demo."""
        self.results.append({
            'demo': demo_name,
            'success': result.success,
            'execution_time': result.execution_time,
            'error': result.error
        })
        
        if result.success:
            print(f"✅ {demo_name} completed successfully!")
            print(f"⏱️ Execution time: {result.execution_time:.2f}s")
            print(f"📄 Result: {result.result[:200]}..." if len(str(result.result)) > 200 else f"📄 Result: {result.result}")
        else:
            print(f"❌ {demo_name} failed!")
            print(f"⏱️ Execution time: {result.execution_time:.2f}s")
            print(f"🚫 Error: {result.error}")
    
    def print_summary(self):
        """Print a summary of all demo results."""
        print("\n" + "="*60)
        print("📊 DEMO SUMMARY")
        print("="*60)
        
        total_demos = len(self.results)
        successful_demos = sum(1 for r in self.results if r['success'])
        total_time = sum(r['execution_time'] for r in self.results)
        
        print(f"📈 Total Demos: {total_demos}")
        print(f"✅ Successful: {successful_demos}")
        print(f"❌ Failed: {total_demos - successful_demos}")
        print(f"⏱️ Total Execution Time: {total_time:.2f}s")
        print(f"📊 Success Rate: {(successful_demos/total_demos)*100:.1f}%")
        
        print("\n📋 Individual Results:")
        for i, result in enumerate(self.results, 1):
            status = "✅" if result['success'] else "❌"
            print(f"{i}. {status} {result['demo']} ({result['execution_time']:.2f}s)")
            if not result['success']:
                print(f"   Error: {result['error']}")

async def run_comprehensive_demo(headless: bool = False):
    """Run the comprehensive Gemini browser automation demo."""
    print("🎯 GEMINI BROWSER AUTOMATION - COMPREHENSIVE DEMO")
    print("="*60)
    print("This demo showcases the capabilities of Gemini AI for browser automation")
    print("including navigation, form interaction, search, and complex workflows.")
    print()
    
    # Check for API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No Gemini API key found!")
        print("💡 Please set your API key:")
        print("   $env:GEMINI_API_KEY='your-api-key-here'")
        print("   or")
        print("   $env:GOOGLE_API_KEY='your-api-key-here'")
        return
    
    print(f"✅ Found API key: {api_key[:10]}...")
    print(f"🖥️ Headless mode: {'ON' if headless else 'OFF'}")
    
    demo_suite = GeminiDemoSuite(headless=headless)
    
    # Setup
    if not await demo_suite.setup():
        print("❌ Failed to setup demo suite")
        return
    
    # Run demos
    demos = [
        demo_suite.demo_1_basic_navigation,
        demo_suite.demo_2_form_interaction,
        demo_suite.demo_3_search_and_extract,
        demo_suite.demo_4_ecommerce_simulation,
        demo_suite.demo_5_complex_navigation
    ]
    
    print(f"\n🚀 Running {len(demos)} comprehensive demos...")
    
    for i, demo in enumerate(demos, 1):
        try:
            print(f"\n⏳ Starting Demo {i}/{len(demos)}...")
            await demo()
            print(f"✅ Demo {i} completed")
        except Exception as e:
            print(f"❌ Demo {i} crashed: {e}")
        
        # Small delay between demos
        if i < len(demos):
            print("⏸️ Pausing 3 seconds before next demo...")
            await asyncio.sleep(3)
    
    # Print summary
    demo_suite.print_summary()
    
    print("\n🎉 Comprehensive demo completed!")
    print("💡 This demonstrates Gemini's capabilities for intelligent browser automation")

async def quick_demo():
    """Quick demo for testing."""
    print("🚀 Quick Gemini Browser Demo")
    print("="*40)
    
    try:
        agent = GeminiBrowserAgent(headless=False, debug=True)
        
        task = "Go to httpbin.org and tell me what you see on the main page"
        print(f"🎯 Task: {task}")
        
        result = await agent.execute_task(task)
        
        if result.success:
            print(f"✅ Success: {result.result}")
        else:
            print(f"❌ Error: {result.error}")
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def main():
    """Main function to run demos."""
    print("🤖 Gemini Browser Automation Demo Suite")
    print("="*50)
    
    try:
        choice = input("Choose demo type:\n1. Quick Demo\n2. Comprehensive Demo (Headless)\n3. Comprehensive Demo (Visual)\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            asyncio.run(quick_demo())
        elif choice == "2":
            asyncio.run(run_comprehensive_demo(headless=True))
        elif choice == "3":
            asyncio.run(run_comprehensive_demo(headless=False))
        else:
            print("Invalid choice. Running quick demo...")
            asyncio.run(quick_demo())
            
    except (EOFError, KeyboardInterrupt):
        print("\n👋 Demo cancelled by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    main()
