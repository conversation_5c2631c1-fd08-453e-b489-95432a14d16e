#!/usr/bin/env python3
"""
Working Visual Marking Demo
Uses the existing BrowserUseAgent with proper Gemini integration
Shows browser-use's built-in visual marking system in action
"""

import asyncio
import os
from browser_use_agent import BrowserUseAgent

async def working_visual_demo():
    """Working demo with visual marking using the existing agent."""
    print("🎨 WORKING VISUAL MARKING DEMO")
    print("="*50)
    print("Using the working BrowserUseAgent with Gemini integration")
    print("You'll see browser-use's built-in visual marking system!")
    print()
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No Gemini API key found!")
        print("💡 Please set your API key:")
        print("   $env:GEMINI_API_KEY='your-api-key-here'")
        return
    
    print(f"✅ Found API key: {api_key[:10]}...")
    
    try:
        # Create the working agent
        print("\n🤖 Creating BrowserUseAgent with Gemini...")
        agent = BrowserUseAgent()  # Will auto-select Gemini if available
        print(f"✅ Agent created with model: {agent.llm.model}")
        
        # Demo 1: Simple navigation with visual marking
        print("\n📍 DEMO 1: Navigation with Visual Element Marking")
        print("-" * 55)
        
        task1 = """
        Navigate to httpbin.org and examine the page structure.
        Look at all the different sections and interactive elements.
        Tell me what you can see on the page and how the elements are organized.
        Take your time to observe the visual marking of elements.
        """
        
        print("🎯 Task: Navigate and observe visual marking")
        print("🔄 Starting navigation...")
        print("👀 WATCH THE BROWSER: Elements will be highlighted with colored borders!")
        
        result1 = await agent.execute_task(task1, headless=False)  # Visual mode
        
        if result1.success:
            print("✅ Demo 1 completed successfully!")
            print("📄 Agent's observations:")
            print("-" * 40)
            print(result1.result)
            print("-" * 40)
        else:
            print(f"❌ Demo 1 failed: {result1.error}")
        
        print("\n⏸️ Pausing 8 seconds to observe the visual marking...")
        await asyncio.sleep(8)
        
        # Demo 2: Form interaction with visual feedback
        print("\n📝 DEMO 2: Form Interaction with Visual Marking")
        print("-" * 55)
        
        task2 = """
        Navigate to httpbin.org/forms/post and interact with the form elements.
        
        1. First, examine all the form fields and tell me what you see
        2. Fill in the customer name field with "Visual Demo User"
        3. Fill in the email field with "<EMAIL>"
        4. Select "Large" for the pizza size
        5. Select "pepperoni" for the topping
        6. Add a comment: "Testing visual marking system"
        7. DO NOT submit the form - just fill it out
        
        Pay attention to how each form element gets highlighted as you interact with it.
        """
        
        print("🎯 Task: Form interaction with visual feedback")
        print("🔄 Starting form interaction...")
        print("👀 WATCH: Form elements will be highlighted as they're selected!")
        
        result2 = await agent.execute_task(task2, headless=False)
        
        if result2.success:
            print("✅ Demo 2 completed successfully!")
            print("📄 Agent's form interaction report:")
            print("-" * 40)
            print(result2.result)
            print("-" * 40)
        else:
            print(f"❌ Demo 2 failed: {result2.error}")
        
        print("\n⏸️ Pausing 8 seconds to observe the filled form...")
        await asyncio.sleep(8)
        
        # Demo 3: Multi-element navigation
        print("\n🔍 DEMO 3: Multi-Element Navigation and Analysis")
        print("-" * 55)
        
        task3 = """
        Navigate to httpbin.org (main page) and explore different sections:
        
        1. Look at the "HTTP Methods" section and click on one of the methods (like GET or POST)
        2. Examine what you see on that page
        3. Go back to the main page
        4. Look at the "Auth" section and click on one of the auth methods
        5. Tell me about the different types of testing endpoints this site provides
        
        Throughout this process, observe how the visual marking helps identify clickable elements.
        """
        
        print("🎯 Task: Multi-step navigation with visual guidance")
        print("🔄 Starting multi-step navigation...")
        print("👀 WATCH: Different element types get different visual treatments!")
        
        result3 = await agent.execute_task(task3, headless=False)
        
        if result3.success:
            print("✅ Demo 3 completed successfully!")
            print("📄 Agent's navigation analysis:")
            print("-" * 40)
            print(result3.result)
            print("-" * 40)
        else:
            print(f"❌ Demo 3 failed: {result3.error}")
        
        # Summary
        print("\n🎉 VISUAL MARKING DEMO COMPLETED!")
        print("="*50)
        
        successful_demos = sum([
            result1.success if 'result1' in locals() else False,
            result2.success if 'result2' in locals() else False,
            result3.success if 'result3' in locals() else False
        ])
        
        print(f"📊 Results: {successful_demos}/3 demos successful")
        print()
        print("🌟 What you've experienced:")
        print("✅ Browser-use's built-in visual element marking")
        print("✅ Colored borders highlighting interactive elements")
        print("✅ Real-time visual feedback during interactions")
        print("✅ Gemini AI making intelligent navigation decisions")
        print("✅ Form field detection and interaction")
        print("✅ Multi-step workflow with visual guidance")
        print()
        print("💡 The visual marking system provides:")
        print("   • Immediate visual feedback for debugging")
        print("   • Clear identification of interactive elements")
        print("   • Numbered elements for precise targeting")
        print("   • Different visual styles for different element types")
        print("   • Real-time highlighting during automation")
        
        print("\n⏸️ Keeping browser open for 15 seconds for final observation...")
        await asyncio.sleep(15)
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()

async def quick_marking_test():
    """Quick test to show visual marking."""
    print("🧪 QUICK VISUAL MARKING TEST")
    print("="*35)
    
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No API key found")
        return
    
    try:
        agent = BrowserUseAgent()
        print(f"✅ Agent ready with model: {agent.llm.model}")
        
        task = """
        Go to httpbin.org and just look around the main page.
        Tell me what interactive elements you can see and how they are visually marked.
        Focus on observing the visual highlighting system.
        """
        
        print("🎯 Quick task: Observe visual marking on httpbin.org")
        print("👀 Watch for element highlighting!")
        
        result = await agent.execute_task(task, headless=False)
        
        if result.success:
            print("✅ Quick test successful!")
            print(f"📄 Observations: {result.result}")
        else:
            print(f"❌ Quick test failed: {result.error}")
        
        print("\n⏸️ Browser will stay open for 10 seconds...")
        await asyncio.sleep(10)
        
    except Exception as e:
        print(f"❌ Quick test failed: {e}")

def main():
    """Main function."""
    print("🎨 WORKING VISUAL MARKING DEMONSTRATION")
    print("="*50)
    print("Experience browser-use's visual marking with Gemini AI!")
    print()
    
    try:
        choice = input("""
Choose demonstration:
1. Quick Visual Test (2-3 minutes)
2. Full Visual Demo (10-15 minutes)
3. Exit

Enter choice (1-3): """).strip()
        
        if choice == "1":
            asyncio.run(quick_marking_test())
        elif choice == "2":
            asyncio.run(working_visual_demo())
        elif choice == "3":
            print("👋 Goodbye!")
            return
        else:
            print("Invalid choice. Running quick test...")
            asyncio.run(quick_marking_test())
            
    except (EOFError, KeyboardInterrupt):
        print("\n👋 Demo cancelled by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    main()
