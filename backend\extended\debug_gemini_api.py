#!/usr/bin/env python3
"""
Debug Gemini API issues and test different configurations.
"""

import os
import google.generativeai as genai

def test_api_key(api_key):
    """Test API key with different configurations."""
    print(f"🔑 Testing API key: {api_key[:10]}...")
    
    try:
        # Configure Gemini
        genai.configure(api_key=api_key)
        
        # List available models
        print("\n📋 Available models:")
        models = list(genai.list_models())
        for model in models[:5]:  # Show first 5
            if 'generateContent' in model.supported_generation_methods:
                print(f"  ✅ {model.name}")
        
        # Test with the most basic model
        print("\n🧪 Testing text generation...")
        
        # Try different model names
        models_to_try = [
            'gemini-1.5-flash',
            'gemini-1.5-pro', 
            'gemini-pro',
            'models/gemini-1.5-flash',
            'models/gemini-pro'
        ]
        
        for model_name in models_to_try:
            try:
                print(f"\n🔄 Trying {model_name}...")
                model = genai.GenerativeModel(model_name)
                response = model.generate_content("Hello, say hi back!")
                print(f"✅ SUCCESS with {model_name}!")
                print(f"📝 Response: {response.text}")
                return True, model_name
                
            except Exception as e:
                error_msg = str(e)
                if "quota" in error_msg.lower():
                    print(f"❌ {model_name}: QUOTA ISSUE - {error_msg[:100]}...")
                elif "404" in error_msg:
                    print(f"❌ {model_name}: MODEL NOT FOUND")
                elif "403" in error_msg:
                    print(f"❌ {model_name}: PERMISSION DENIED")
                else:
                    print(f"❌ {model_name}: {error_msg[:100]}...")
        
        return False, None
        
    except Exception as e:
        print(f"❌ API Configuration Error: {e}")
        return False, None

def main():
    """Main debug function."""
    print("🔍 Gemini API Debug Tool")
    print("=" * 40)
    
    # Get API key from environment or user input
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    
    if not api_key:
        print("❌ No API key found in environment variables.")
        print("💡 Please set your API key:")
        print("   $env:GEMINI_API_KEY='your-api-key-here'")
        return
    
    # Test the API key
    success, working_model = test_api_key(api_key)
    
    if success:
        print(f"\n🎉 SUCCESS! Working model: {working_model}")
        print("\n💡 Your API key is working correctly!")
        print("You can now use browser automation with Gemini.")
    else:
        print("\n❌ All models failed.")
        print("\n🔧 Troubleshooting steps:")
        print("1. Verify your API key is correct")
        print("2. Check if your Google account is verified")
        print("3. Try waiting a few minutes and test again")
        print("4. Check Google AI Studio for any account issues")
        print("5. Consider creating another new account if issues persist")

if __name__ == "__main__":
    main()
