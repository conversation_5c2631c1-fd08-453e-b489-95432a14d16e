# SmartSurf Ollama Integration

This directory contains the Ollama integration for SmartSurf's browser automation agent. The integration allows you to use local LLM models through Ollama instead of cloud-based APIs.

## 🚀 Quick Start

### 1. Install Ollama

Visit [ollama.ai](https://ollama.ai/download) or run:
```bash
curl -fsSL https://ollama.ai/install.sh | sh
```

### 2. Start Ollama Service

```bash
ollama serve
```

### 3. Run Setup Script

```bash
cd backend/extended
python setup_ollama.py
```

### 4. Test Integration

```bash
python test_ollama_integration.py
```

### 5. Use in Your Code

```python
from browser_use_agent import BrowserUseAgent

# Create agent with default Ollama model
agent = BrowserUseAgent()

# Or specify a custom model
agent = BrowserUseAgent(ollama_model="codellama")

# Execute a browser task
result = await agent.execute_task("Go to google.com and search for Python")
```

## 📦 Recommended Models

The setup script will help you install these recommended models:

- **llama3.2** - Good general purpose model (default)
- **codellama** - Optimized for code-related tasks
- **llama3.1** - Alternative general purpose model

To manually install a model:
```bash
ollama pull llama3.2
```

## ⚙️ Configuration

The integration uses environment variables for configuration. Create a `.env` file:

```env
# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama3.2

# Set to true if you want to use OpenAI instead
USE_OPENAI=false
# OPENAI_API_KEY=your_openai_api_key_here
```

## 🔧 Advanced Usage

### Custom Ollama Instance

```python
from browser_use_agent import OllamaChatModel, BrowserUseAgent

# Custom Ollama configuration
custom_llm = OllamaChatModel(
    model="codellama",
    base_url="http://your-ollama-server:11434"
)

agent = BrowserUseAgent(llm=custom_llm)
```

### Different Models for Different Tasks

```python
# Use codellama for programming tasks
coding_agent = BrowserUseAgent(ollama_model="codellama")

# Use llama3.2 for general web automation
general_agent = BrowserUseAgent(ollama_model="llama3.2")
```

## 🐛 Troubleshooting

### Ollama Not Running
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama if not running
ollama serve
```

### Model Not Found
```bash
# List available models
ollama list

# Pull a missing model
ollama pull llama3.2
```

### Connection Issues
- Ensure Ollama is running on the correct port (default: 11434)
- Check firewall settings if using a remote Ollama instance
- Verify the base URL in your configuration

### Performance Issues
- Use smaller models for faster responses (e.g., llama3.2 vs llama3.1)
- Ensure sufficient RAM (8GB+ recommended for most models)
- Consider using GPU acceleration if available

## 📋 Requirements

- Python 3.8+
- Ollama installed and running
- browser-use library
- langchain-core
- requests

## 🔗 Useful Links

- [Ollama Documentation](https://ollama.ai/docs)
- [Available Models](https://ollama.ai/library)
- [Browser-Use Library](https://github.com/gregpr07/browser-use)

## 📝 Notes

- The first run may be slower as models need to load into memory
- Larger models provide better results but require more resources
- Local inference means no data is sent to external APIs
- Models are cached locally after first download
