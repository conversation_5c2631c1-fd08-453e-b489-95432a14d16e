#!/usr/bin/env python3
"""
WORKING Gemini Browser Integration Demo
Uses the existing BrowserUseAgent with Gemini integration
"""

import asyncio
import os
from browser_use_agent import BrowserUseAgent

async def demo_gemini_navigation():
    """Demo: Gemini-powered browser navigation."""
    print("🚀 WORKING GEMINI BROWSER AUTOMATION DEMO")
    print("="*60)
    
    # Check API key
    api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ No Gemini API key found!")
        print("💡 Please set your API key:")
        print("   $env:GEMINI_API_KEY='your-api-key-here'")
        print("   or")
        print("   $env:GOOGLE_API_KEY='your-api-key-here'")
        return
    
    print(f"✅ Found API key: {api_key[:10]}...")
    
    try:
        # Create Gemini-powered browser agent
        print("\n🤖 Creating Gemini Browser Agent...")
        agent = BrowserUseAgent()  # Will auto-select Gemini if API key is available
        print(f"✅ Agent created with model: {agent.llm.model}")
        
        # Demo 1: Simple navigation
        print("\n📍 DEMO 1: Simple Navigation")
        print("-" * 40)
        task1 = "Navigate to httpbin.org and tell me what this website is for and what services it provides"
        print(f"🎯 Task: {task1}")
        print("🔄 Executing...")
        
        result1 = await agent.execute_task(task1, headless=False)  # Show browser window
        
        if result1.success:
            print("✅ Demo 1 completed successfully!")
            print(f"📄 Result: {result1.result}")
        else:
            print(f"❌ Demo 1 failed: {result1.error}")
        
        print("\n⏸️ Pausing 3 seconds before next demo...")
        await asyncio.sleep(3)
        
        # Demo 2: Form interaction
        print("\n📝 DEMO 2: Form Interaction")
        print("-" * 40)
        task2 = """Navigate to httpbin.org/forms/post and fill out the form with this information:
        - Customer name: Gemini Test User
        - Telephone: 555-0123
        - Email: <EMAIL>
        - Size: Large
        - Topping: pepperoni
        - Delivery time: now
        - Comments: This form was filled by Gemini AI
        
        After filling the form, submit it and tell me what response you get."""
        
        print(f"🎯 Task: Form filling and submission")
        print("🔄 Executing...")
        
        result2 = await agent.execute_task(task2, headless=False)
        
        if result2.success:
            print("✅ Demo 2 completed successfully!")
            print(f"📄 Result: {result2.result}")
        else:
            print(f"❌ Demo 2 failed: {result2.error}")
        
        print("\n⏸️ Pausing 3 seconds before next demo...")
        await asyncio.sleep(3)
        
        # Demo 3: Search and analysis
        print("\n🔍 DEMO 3: Search and Analysis")
        print("-" * 40)
        task3 = """Go to Wikipedia and perform these steps:
        1. Search for 'Artificial Intelligence'
        2. Click on the main AI article
        3. Read the introduction section
        4. Find and click on a related topic (like 'Machine Learning' or 'Deep Learning')
        5. Provide a summary of what you learned about AI and the related topic
        
        Be descriptive about each step you take."""
        
        print(f"🎯 Task: Wikipedia search and analysis")
        print("🔄 Executing...")
        
        result3 = await agent.execute_task(task3, headless=False)
        
        if result3.success:
            print("✅ Demo 3 completed successfully!")
            print(f"📄 Result: {result3.result}")
        else:
            print(f"❌ Demo 3 failed: {result3.error}")
        
        # Summary
        print("\n🎉 ALL DEMOS COMPLETED!")
        print("="*60)
        
        successful_demos = sum([result1.success, result2.success, result3.success])
        print(f"📊 Results: {successful_demos}/3 demos successful")
        
        if successful_demos == 3:
            print("🌟 Perfect! All demos worked successfully!")
        elif successful_demos >= 2:
            print("👍 Great! Most demos worked successfully!")
        else:
            print("⚠️ Some demos had issues, but the integration is working!")
        
        print("\n💡 This demonstrates:")
        print("✅ Gemini AI integration with browser automation")
        print("✅ Intelligent web navigation and interaction")
        print("✅ Form filling with natural language instructions")
        print("✅ Search and content analysis capabilities")
        print("✅ Multi-step workflow execution")
        
    except Exception as e:
        print(f"❌ Demo failed with exception: {e}")
        import traceback
        traceback.print_exc()

async def quick_test():
    """Quick test to verify Gemini integration is working."""
    print("🧪 QUICK GEMINI TEST")
    print("="*30)
    
    try:
        agent = BrowserUseAgent()
        print(f"✅ Agent created with model: {agent.llm.model}")
        
        task = "Go to httpbin.org and tell me the page title"
        print(f"🎯 Quick task: {task}")
        
        result = await agent.execute_task(task, headless=True)
        
        if result.success:
            print(f"✅ Success: {result.result}")
        else:
            print(f"❌ Failed: {result.error}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

def main():
    """Main function."""
    print("🤖 Gemini Browser Integration - Working Demo")
    print("="*50)
    
    try:
        choice = input("""
Choose demo type:
1. Quick Test (headless)
2. Full Demo (visual browser)
3. Exit

Enter choice (1-3): """).strip()
        
        if choice == "1":
            asyncio.run(quick_test())
        elif choice == "2":
            asyncio.run(demo_gemini_navigation())
        elif choice == "3":
            print("👋 Goodbye!")
            return
        else:
            print("Invalid choice. Running quick test...")
            asyncio.run(quick_test())
            
    except (EOFError, KeyboardInterrupt):
        print("\n👋 Demo cancelled by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    main()
