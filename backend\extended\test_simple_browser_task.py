#!/usr/bin/env python3
"""
Test a very simple browser task to debug the integration.
"""

import asyncio
import os
from browser_use_agent import BrowserUseAgent

async def test_simple_task():
    """Test with a very simple task."""
    print("🧪 Testing Simple Browser Task")
    print("=" * 40)
    
    try:
        # Set API key
        os.environ["GEMINI_API_KEY"] = "AIzaSyBEWog2PpUjLK64XW60niePRxtU1o8HWVM"
        
        # Create agent
        agent = BrowserUseAgent()
        print(f"🤖 Using model: {agent.llm.model}")
        
        # Very simple task
        task = "Go to https://example.com"
        
        print(f"\n📋 Simple Task: {task}")
        print("🔄 Executing...")
        
        result = await agent.execute_task(task, headless=True)
        
        if result.success:
            print("✅ Task completed successfully!")
            print(f"📄 Result: {result.result}")
        else:
            print(f"❌ Task failed: {result.error}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple_task())
