#!/usr/bin/env python3
"""
Example usage of SmartSurf with Ollama integration.

This script demonstrates how to use the OllamaChatModel for various tasks
without requiring browser automation.
"""

import asyncio
from browser_use_agent import OllamaChatModel, BrowserUseAgent
from langchain_core.messages import HumanMessage

def example_direct_ollama():
    """Example of using OllamaChatModel directly."""
    print("🤖 Direct Ollama Usage Example")
    print("=" * 40)
    
    # Create an Ollama model
    model = OllamaChatModel(model="llama3.2")
    print(f"✅ Created model: {model.model}")
    
    # Example 1: Simple text generation
    try:
        print("\n📝 Example 1: Simple text generation")
        messages = [HumanMessage(content="Write a short greeting message.")]
        result = model._generate(messages)
        response = result.generations[0].message.content
        print(f"Response: {response}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Example 2: Code generation
    try:
        print("\n💻 Example 2: Code generation")
        messages = [HumanMessage(content="Write a simple Python function to add two numbers.")]
        result = model._generate(messages)
        response = result.generations[0].message.content
        print(f"Response: {response}")
    except Exception as e:
        print(f"❌ Error: {e}")

def example_browser_agent_init():
    """Example of initializing BrowserUseAgent with Ollama."""
    print("\n🌐 Browser Agent with Ollama Example")
    print("=" * 40)
    
    # Create agent with default Ollama model
    agent1 = BrowserUseAgent()
    print(f"✅ Created agent with default model: {agent1.llm.model}")
    
    # Create agent with specific Ollama model
    agent2 = BrowserUseAgent(ollama_model="llama2")
    print(f"✅ Created agent with llama2 model: {agent2.llm.model}")
    
    # Create agent with custom Ollama instance
    custom_llm = OllamaChatModel(model="llama3.2", base_url="http://localhost:11434")
    agent3 = BrowserUseAgent(llm=custom_llm)
    print(f"✅ Created agent with custom LLM: {agent3.llm.model}")

async def example_browser_task():
    """Example of a browser automation task (requires browser-use)."""
    print("\n🔧 Browser Automation Example")
    print("=" * 40)
    
    try:
        # Create agent
        agent = BrowserUseAgent(ollama_model="llama3.2")
        
        # Simple task
        task = "Navigate to https://httpbin.org/get and tell me what you see"
        print(f"📋 Task: {task}")
        
        # Execute task
        result = await agent.execute_task(task, headless=True)
        
        if result.success:
            print("✅ Task completed successfully!")
            print(f"📄 Result: {result.result}")
        else:
            print(f"❌ Task failed: {result.error}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def example_different_models():
    """Example of using different Ollama models for different tasks."""
    print("\n🎯 Different Models for Different Tasks")
    print("=" * 40)
    
    # General purpose model
    general_model = OllamaChatModel(model="llama3.2")
    print(f"📝 General model: {general_model.model}")
    
    # Code-focused model (if available)
    try:
        code_model = OllamaChatModel(model="codellama")
        print(f"💻 Code model: {code_model.model}")
    except:
        print("💻 Code model (codellama) not available")
    
    # Smaller/faster model
    try:
        fast_model = OllamaChatModel(model="llama2")
        print(f"⚡ Fast model: {fast_model.model}")
    except:
        print("⚡ Fast model (llama2) not available")

def main():
    """Run all examples."""
    print("🚀 SmartSurf Ollama Usage Examples")
    print("=" * 50)
    
    # Example 1: Direct Ollama usage
    example_direct_ollama()
    
    # Example 2: Browser agent initialization
    example_browser_agent_init()
    
    # Example 3: Different models
    example_different_models()
    
    # Example 4: Browser automation (optional)
    print("\n❓ Would you like to test browser automation?")
    print("(Requires working browser-use installation)")
    
    try:
        choice = input("Test browser automation? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            asyncio.run(example_browser_task())
        else:
            print("Skipping browser automation example.")
    except (EOFError, KeyboardInterrupt):
        print("Skipping browser automation example.")
    
    print("\n🎉 Examples complete!")
    print("\n💡 Next steps:")
    print("1. Customize models in your .env file")
    print("2. Install browser-use for full automation: pip install browser-use")
    print("3. Check the README_OLLAMA.md for more details")

if __name__ == "__main__":
    main()
