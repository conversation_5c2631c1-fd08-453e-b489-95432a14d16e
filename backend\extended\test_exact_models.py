#!/usr/bin/env python3
"""
Test with the exact model names that are available.
"""

import os
import google.generativeai as genai

def test_exact_models():
    """Test with exact model names from the available list."""
    api_key = "AIzaSyBEWog2PpUjLK64XW60niePRxtU1o8HWVM"
    
    print(f"🔑 Testing API key: {api_key[:10]}...")
    
    try:
        genai.configure(api_key=api_key)
        
        # Test with the exact model names we saw in the list
        exact_models = [
            "models/gemini-1.5-pro-latest",
            "models/gemini-1.5-pro-002", 
            "models/gemini-1.5-pro",
            "models/gemini-1.5-flash-latest"
        ]
        
        for model_name in exact_models:
            try:
                print(f"\n🔄 Testing {model_name}...")
                model = genai.GenerativeModel(model_name)
                response = model.generate_content("Hello! Just say 'Hi' back.")
                print(f"✅ SUCCESS with {model_name}!")
                print(f"📝 Response: {response.text}")
                return True, model_name
                
            except Exception as e:
                error_msg = str(e)
                if "quota" in error_msg.lower() or "429" in error_msg:
                    print(f"❌ {model_name}: QUOTA EXCEEDED")
                elif "404" in error_msg:
                    print(f"❌ {model_name}: NOT FOUND")
                elif "403" in error_msg:
                    print(f"❌ {model_name}: PERMISSION DENIED")
                else:
                    print(f"❌ {model_name}: {error_msg[:100]}...")
        
        return False, None
        
    except Exception as e:
        print(f"❌ Configuration Error: {e}")
        return False, None

if __name__ == "__main__":
    print("🧪 Testing Exact Gemini Models")
    print("=" * 40)
    
    success, working_model = test_exact_models()
    
    if success:
        print(f"\n🎉 SUCCESS! Working model: {working_model}")
    else:
        print("\n❌ All exact models failed.")
        print("\n💡 This suggests the account may need:")
        print("1. Email/phone verification")
        print("2. Billing setup (even for free tier)")
        print("3. Regional access permissions")
        print("4. Account age (some new accounts have restrictions)")
