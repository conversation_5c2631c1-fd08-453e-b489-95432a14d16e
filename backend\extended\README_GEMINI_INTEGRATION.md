# 🤖 Gemini Browser Integration for WebRover

Complete integration of Google Gemini AI with browser automation for intelligent web navigation and interaction.

## 🚀 Features

- **Intelligent Navigation**: Gemini AI understands and executes complex web navigation tasks
- **Visual Element Detection**: Advanced element recognition and interaction
- **Form Automation**: Smart form filling with natural language instructions
- **Search & Analysis**: Comprehensive web search and content extraction
- **Error Recovery**: Intelligent error handling and alternative approaches
- **Visual Marking**: See browser automation in action with visual element highlighting
- **Multi-step Workflows**: Execute complex, multi-page automation sequences

## 📋 Requirements

- Python 3.8+
- Google Gemini API key
- Chrome/Chromium browser

## 🛠️ Installation

1. **Install dependencies**:
   ```bash
   pip install google-generativeai langchain-google-genai browser-use
   ```

2. **Get Gemini API key**:
   - Visit: https://makersuite.google.com/app/apikey
   - Create a new API key
   - Copy the key

3. **Set environment variable**:
   ```powershell
   # PowerShell (Windows)
   $env:GEMINI_API_KEY='your-api-key-here'
   
   # Bash (Linux/Mac)
   export GEMINI_API_KEY='your-api-key-here'
   ```

## 🧪 Quick Test

Run the setup test to verify everything is working:

```bash
python test_gemini_setup.py
```

This will:
- ✅ Check all dependencies
- ✅ Test Gemini API connection
- ✅ Verify browser-use integration
- ✅ Run a quick demo

## 🎬 Demos

### 1. Navigation Demo
```bash
python gemini_navigation_demo.py
```

**Features demonstrated**:
- Wikipedia navigation and information extraction
- Form filling and submission
- Search and content analysis
- Multi-step workflows

### 2. Comprehensive Demo Suite
```bash
python gemini_demo_comprehensive.py
```

**Includes**:
- Basic navigation and page analysis
- Form interaction and submission
- Search and information extraction
- E-commerce browsing simulation
- Complex multi-step navigation

## 💻 Usage Examples

### Basic Usage

```python
from gemini_browser_integration import GeminiBrowserAgent

# Create agent
agent = GeminiBrowserAgent(
    model="gemini-2.0-flash-exp",
    headless=False,  # Show browser window
    debug=True
)

# Execute a task
result = await agent.execute_task(
    "Go to Wikipedia and search for 'Machine Learning', then summarize the main article"
)

if result.success:
    print(f"Result: {result.result}")
else:
    print(f"Error: {result.error}")
```

### Advanced Usage

```python
# Form filling
result = await agent.form_interaction(
    url="https://httpbin.org/forms/post",
    form_data={
        "custname": "John Doe",
        "custtel": "555-1234",
        "custemail": "<EMAIL>"
    }
)

# Search and extract
result = await agent.search_and_extract(
    "browser automation tools 2024",
    search_engine="google.com"
)

# Page analysis
result = await agent.navigate_and_analyze("https://example.com")
```

### Convenience Functions

```python
from gemini_browser_integration import gemini_browse, gemini_search, gemini_analyze_page

# Quick browsing
result = await gemini_browse("Navigate to GitHub and find popular Python repositories")

# Quick search
result = await gemini_search("artificial intelligence trends 2024")

# Quick analysis
result = await gemini_analyze_page("https://news.ycombinator.com")
```

## 🔧 Configuration

### Model Options
- `gemini-2.0-flash-exp` (default) - Latest experimental model
- `gemini-1.5-pro` - Production model with high capability
- `gemini-1.5-flash` - Fast model for simple tasks

### Browser Options
- `headless=False` - Show browser window (recommended for demos)
- `headless=True` - Run in background (faster for production)
- `debug=True` - Enable detailed logging

### Example Configuration
```python
agent = GeminiBrowserAgent(
    model="gemini-2.0-flash-exp",
    headless=False,
    debug=True
)
```

## 📊 Performance

Typical execution times:
- Simple navigation: 5-15 seconds
- Form filling: 10-20 seconds
- Search and analysis: 15-30 seconds
- Complex workflows: 30-60 seconds

## 🐛 Troubleshooting

### Common Issues

1. **API Key Error**
   ```
   ValueError: Gemini API key required
   ```
   **Solution**: Set `GEMINI_API_KEY` environment variable

2. **Import Error**
   ```
   ImportError: No module named 'google.generativeai'
   ```
   **Solution**: `pip install google-generativeai langchain-google-genai`

3. **Browser Error**
   ```
   ImportError: No module named 'browser_use'
   ```
   **Solution**: `pip install browser-use`

4. **Chrome Not Found**
   ```
   Browser not found
   ```
   **Solution**: Install Chrome or Chromium browser

### Debug Mode

Enable debug mode for detailed logging:
```python
agent = GeminiBrowserAgent(debug=True)
```

## 🎯 Use Cases

### Web Scraping
```python
task = "Go to news.ycombinator.com and extract the top 5 story titles and their URLs"
result = await agent.execute_task(task)
```

### Form Automation
```python
task = "Fill out the contact form with my information and submit it"
result = await agent.execute_task(task, url="https://example.com/contact")
```

### Research Tasks
```python
task = "Research the latest developments in quantum computing and provide a summary"
result = await agent.execute_task(task)
```

### E-commerce Testing
```python
task = "Browse the product catalog, add items to cart, and proceed to checkout (don't complete purchase)"
result = await agent.execute_task(task, url="https://demo-store.com")
```

## 🔮 Advanced Features

### Custom Prompts
The integration automatically enhances prompts for better performance:
- Adds step-by-step instructions
- Includes error handling guidance
- Optimizes for visual element detection

### Screenshot Analysis
Gemini can analyze screenshots to understand page content and make intelligent decisions about next actions.

### Multi-page Workflows
Handle complex workflows that span multiple pages and require maintaining context across navigation.

## 📈 Best Practices

1. **Use descriptive tasks**: Be specific about what you want to accomplish
2. **Start simple**: Test with basic navigation before complex workflows
3. **Enable visual mode**: Use `headless=False` to see what's happening
4. **Handle errors gracefully**: Check `result.success` before using results
5. **Use appropriate timeouts**: Complex tasks may take longer

## 🤝 Integration with WebRover

This Gemini integration is designed to work seamlessly with the existing WebRover architecture:

- Compatible with existing browser automation
- Extends current capabilities with AI intelligence
- Maintains the same result format for easy integration
- Can be used alongside existing Ollama integration

## 📚 API Reference

### GeminiBrowserAgent

**Constructor**:
- `model`: Gemini model name
- `api_key`: API key (optional if set in environment)
- `headless`: Browser visibility
- `debug`: Enable debug logging

**Methods**:
- `execute_task(task, url=None, max_steps=10)`: Execute automation task
- `navigate_and_analyze(url)`: Navigate and analyze page
- `search_and_extract(query, search_engine)`: Search and extract info
- `form_interaction(url, form_data)`: Fill and submit forms

### BrowserTaskResult

**Attributes**:
- `success`: Boolean indicating success/failure
- `result`: Task result or None
- `error`: Error message if failed
- `execution_time`: Time taken in seconds

## 🎉 Getting Started

1. Run the setup test: `python test_gemini_setup.py`
2. Try the navigation demo: `python gemini_navigation_demo.py`
3. Explore comprehensive demos: `python gemini_demo_comprehensive.py`
4. Integrate into your own projects using the examples above

The integration provides a powerful combination of Gemini's intelligence with browser automation capabilities, enabling sophisticated web interaction scenarios that were previously difficult to achieve.
