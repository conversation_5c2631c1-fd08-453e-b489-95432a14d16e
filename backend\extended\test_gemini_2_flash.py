#!/usr/bin/env python3
"""
Test the newer Gemini 2.0 Flash model.
"""

import google.generativeai as genai

def test_gemini_2_flash():
    """Test Gemini 2.0 Flash model."""
    api_key = "AIzaSyBEWog2PpUjLK64XW60niePRxtU1o8HWVM"
    
    print("🧪 Testing Gemini 2.0 Flash Model")
    print("=" * 40)
    
    try:
        genai.configure(api_key=api_key)
        
        # Test the newer model
        model_name = "gemini-2.0-flash"
        print(f"🔄 Testing {model_name}...")
        
        model = genai.GenerativeModel(model_name)
        response = model.generate_content("Explain how AI works in a few words")
        
        print(f"✅ SUCCESS with {model_name}!")
        print(f"📝 Response: {response.text}")
        return True
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Error: {error_msg}")
        
        if "quota" in error_msg.lower() or "429" in error_msg:
            print("💡 Quota issue - try again later or enable billing")
        elif "404" in error_msg:
            print("💡 Model not found - may need different model name")
        elif "403" in error_msg:
            print("💡 Permission denied - check API key permissions")
        
        return False

if __name__ == "__main__":
    test_gemini_2_flash()
