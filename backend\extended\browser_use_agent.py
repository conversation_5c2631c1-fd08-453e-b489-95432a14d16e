"""
SmartSurf Browser Use Agent with Ollama Integration

This module provides a browser automation agent that uses Ollama for local LLM inference.
It's designed to work with the browser-use library for web automation tasks.

Requirements:
- Ollama installed and running (ollama serve)
- browser-use library
- A compatible Ollama model (e.g., llama3.2, codellama, etc.)

Usage:
    agent = BrowserUseAgent(ollama_model="llama3.2")
    result = await agent.execute_task("Go to google.com and search for Python")
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.outputs import LLMResult, Generation
from dotenv import load_dotenv
import asyncio
import requests

# Try to import Google Gemini
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    print("Warning: google-generativeai not available. Install with: pip install google-generativeai")
    GEMINI_AVAILABLE = False
    genai = None

# Try to import browser_use components, but handle gracefully if not available
try:
    from browser_use.agent.service import Agent
    from browser_use.browser.session import BrowserSession
    BROWSER_USE_AVAILABLE = True
    print("✅ browser-use components imported successfully")
except ImportError as e:
    print(f"Warning: browser-use not available: {e}")
    print("Install with: pip install browser-use")
    BROWSER_USE_AVAILABLE = False
    Agent = None
    BrowserSession = None

@dataclass
class BrowserUseResult:
    """Container for browser use execution results."""
    success: bool
    result: Any
    error: Optional[str] = None

class OllamaChatModel:
    """
    A simple wrapper for Ollama models that's compatible with browser-use.
    """

    def __init__(self, model: str = "llama3.2", base_url: str = "http://localhost:11434"):
        self.model = model
        self.base_url = base_url.rstrip("/")
        self._llm_type = "ollama"
        # Add compatibility attributes for browser-use
        self.provider = "ollama"
        self.model_name = model

    def _generate(self, messages, _stop=None, _run_manager=None, **_kwargs):
        """Generate a response from the Ollama model."""
        # Convert LangChain messages to Ollama format
        ollama_messages = []
        for msg in messages:
            if hasattr(msg, 'content'):
                # Handle different content types
                content = msg.content
                if isinstance(content, list):
                    # Handle complex content with multiple parts
                    text_content = ""
                    for part in content:
                        if hasattr(part, 'text'):
                            text_content += part.text
                        elif isinstance(part, dict) and 'text' in part:
                            text_content += part['text']
                        elif isinstance(part, str):
                            text_content += part
                        else:
                            text_content += str(part)
                    content = text_content
                elif not isinstance(content, str):
                    content = str(content)

                role = "user" if msg.__class__.__name__ == "HumanMessage" else "assistant"
                ollama_messages.append({"role": role, "content": content})
            else:
                # Fallback for string messages
                ollama_messages.append({"role": "user", "content": str(msg)})

        payload = {
            "model": self.model,
            "messages": ollama_messages,
            "stream": False
        }

        try:
            response = requests.post(f"{self.base_url}/api/chat", json=payload, timeout=60)
            response.raise_for_status()
            data = response.json()

            # Extract the response content
            content = data.get("message", {}).get("content", "")

            # Return in LangChain format
            from langchain_core.outputs import ChatGeneration, ChatResult
            from langchain_core.messages import AIMessage

            message = AIMessage(content=content)
            generation = ChatGeneration(message=message)
            return ChatResult(generations=[generation])

        except Exception as e:
            raise Exception(f"Error calling Ollama API: {str(e)}")

    def invoke(self, messages):
        """Simple invoke method for compatibility."""
        result = self._generate(messages)
        return result.generations[0].message

    async def ainvoke(self, *args, **kwargs):
        """Async invoke method for compatibility."""
        # Extract messages from args (first argument should be messages)
        messages = args[0] if args else kwargs.get('messages', [])
        # For now, just call the sync version
        # In a real implementation, you might want to use aiohttp for async requests
        return self.invoke(messages)

class GeminiChatModel:
    """
    A wrapper for Google Gemini models that's compatible with browser-use.
    """

    def __init__(self, model: str = "gemini-2.0-flash", api_key: Optional[str] = None):
        self.model = model
        self._llm_type = "gemini"
        # Add compatibility attributes for browser-use
        self.provider = "gemini"
        self.model_name = model

        # Initialize Gemini
        if not GEMINI_AVAILABLE:
            raise ImportError("google-generativeai not available. Install with: pip install google-generativeai")

        # Get API key from parameter or environment
        api_key = api_key or os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("Gemini API key not found. Set GEMINI_API_KEY or GOOGLE_API_KEY environment variable")

        genai.configure(api_key=api_key)
        # Use the model name without the "models/" prefix for the GenerativeModel
        model_name = model.replace("models/", "") if model.startswith("models/") else model
        self.client = genai.GenerativeModel(model_name)

    def _generate(self, messages, _stop=None, _run_manager=None, **_kwargs):
        """Generate a response from the Gemini model."""
        try:
            # Convert LangChain messages to Gemini format
            gemini_messages = []
            for msg in messages:
                if hasattr(msg, 'content'):
                    # Handle different content types
                    content = msg.content
                    if isinstance(content, list):
                        # Handle complex content with multiple parts
                        text_content = ""
                        for part in content:
                            if hasattr(part, 'text'):
                                text_content += part.text
                            elif isinstance(part, dict) and 'text' in part:
                                text_content += part['text']
                            elif isinstance(part, str):
                                text_content += part
                            else:
                                text_content += str(part)
                        content = text_content
                    elif not isinstance(content, str):
                        content = str(content)

                    gemini_messages.append(content)
                else:
                    # Fallback for string messages
                    gemini_messages.append(str(msg))

            # Combine all messages into a single prompt for Gemini
            prompt = "\n".join(gemini_messages)

            # Generate response
            response = self.client.generate_content(prompt)
            content = response.text if response.text else ""

            # Return in LangChain format
            from langchain_core.outputs import ChatGeneration, ChatResult
            from langchain_core.messages import AIMessage

            # Create AIMessage with all attributes browser-use expects
            message = AIMessage(content=content)
            # Add usage attribute with the exact schema browser-use expects
            message.usage = {
                "prompt_tokens": 0,
                "prompt_cached_tokens": 0,
                "prompt_cache_creation_tokens": 0,
                "prompt_image_tokens": 0,
                "completion_tokens": 0,
                "total_tokens": 0
            }
            # Add completion attribute that browser-use also expects
            message.completion = content
            generation = ChatGeneration(message=message)
            return ChatResult(generations=[generation])

        except Exception as e:
            raise Exception(f"Error calling Gemini API: {str(e)}")

    def invoke(self, messages):
        """Simple invoke method for compatibility."""
        result = self._generate(messages)
        return result.generations[0].message

    async def ainvoke(self, *args, **kwargs):
        """Async invoke method for compatibility."""
        # Extract messages from args (first argument should be messages)
        messages = args[0] if args else kwargs.get('messages', [])
        # For now, just call the sync version
        # In a real implementation, you might want to use async Gemini client
        return self.invoke(messages)

class BrowserUseAgent:
    """
    A specialized agent for browser automation tasks using the browser-use library.

    This agent provides a high-level interface for performing web automation tasks
    with built-in error handling and result processing.
    """

    def __init__(self, llm: Optional[BaseChatModel] = None, ollama_model: str = "llama3.2", gemini_model: str = "gemini-2.0-flash"):
        """
        Initialize the BrowserUseAgent.

        Args:
            llm: Optional language model to use. If not provided, will auto-select best available.
            ollama_model: The Ollama model to use (default: llama3.2)
            gemini_model: The Gemini model to use (default: gemini-1.5-flash)
        """
        load_dotenv()  # Load environment variables
        self.llm = llm or self._get_default_llm(ollama_model, gemini_model)

    @staticmethod
    def _get_default_llm(ollama_model: str = "llama3.2", gemini_model: str = "gemini-2.0-flash"):
        """Get the default language model for the agent. Prioritizes Gemini, then OpenAI, then Ollama."""

        # Check for Gemini API key first (most reliable option)
        gemini_api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        if gemini_api_key and GEMINI_AVAILABLE:
            try:
                print(f"🤖 Using Google Gemini: {gemini_model}")
                return GeminiChatModel(model=gemini_model, api_key=gemini_api_key)
            except Exception as e:
                print(f"⚠️  Gemini setup failed: {e}, falling back to other options")

        # Check if user specifically wants to use OpenAI
        openai_api_key = os.getenv("OPENAI_API_KEY")
        use_openai = os.getenv("USE_OPENAI", "false").lower() == "true"

        if openai_api_key and use_openai:
            try:
                from langchain_openai import ChatOpenAI
                print("🔗 Using OpenAI ChatGPT")
                return ChatOpenAI(
                    model="gpt-3.5-turbo",
                    temperature=0,
                    api_key=openai_api_key
                )
            except ImportError:
                print("⚠️  OpenAI package not available, falling back to Ollama")

        # Default to Ollama
        print(f"🦙 Using Ollama model: {ollama_model}")
        return OllamaChatModel(model=ollama_model)

    async def execute_task(
        self,
        task: str,
        headless: bool = True,
        browser_config: Optional[Dict[str, Any]] = None,
        debug: bool = False,
        save_screenshots: bool = True
    ) -> BrowserUseResult:
        """
        Execute a browser automation task with visual element marking.

        Args:
            task: The task description for the agent to execute.
            headless: Whether to run browser in headless mode (False shows visual marking)
            browser_config: Optional configuration for the browser.
            debug: Enable detailed debugging output
            save_screenshots: Save annotated screenshots with element marking

        Returns:
            BrowserUseResult containing the execution results.

        Visual Features (when headless=False):
            - Element highlighting and numbering
            - Bounding boxes around interactive elements
            - Annotated screenshots saved to debug folder
            - Click and input visualization
        """
        if not BROWSER_USE_AVAILABLE:
            return BrowserUseResult(
                success=False,
                result=None,
                error="browser-use library is not available. Install with: pip install browser-use"
            )

        try:
            # Create browser session
            browser_session = BrowserSession()

            # Initialize and run the agent
            agent = Agent(
                task=task,
                llm=self.llm,
                browser=browser_session
            )

            # Execute the task
            result = await agent.run()

            # Handle different result formats
            if hasattr(result, 'final_result'):
                final_result = result.final_result()
            elif hasattr(result, 'result'):
                final_result = result.result
            else:
                final_result = str(result)

            return BrowserUseResult(
                success=True,
                result=final_result
            )

        except Exception as e:
            return BrowserUseResult(
                success=False,
                result=None,
                error=f"Browser automation error: {str(e)}"
            )
    
    async def __call__(self, task: str, **kwargs) -> BrowserUseResult:
        """Make the agent callable for convenience."""
        return await self.execute_task(task, **kwargs)

# Example usage
async def example_usage():
    """Example of how to use the BrowserUseAgent with Ollama."""
    # Create agent with default Ollama model (llama3.2)
    agent = BrowserUseAgent()

    # Or specify a different Ollama model
    # agent = BrowserUseAgent(ollama_model="llama3.1")

    # Or use a custom Ollama instance
    # custom_llm = OllamaChatModel(model="codellama", base_url="http://localhost:11434")
    # agent = BrowserUseAgent(llm=custom_llm)

    # Example task
    task = """
    Go to https://www.python.org
    Find and return the text of the first news item on the page.
    """

    print("Starting browser automation task with Ollama...")
    print(f"Task: {task.strip()}")

    result = await agent(task, headless=True)

    if result.success:
        print("\n✅ Task completed successfully:")
        print(result.result)
    else:
        print(f"\n❌ Error: {result.error}")

def test_ollama_connection():
    """Test if Ollama is running and accessible."""
    try:
        ollama_model = OllamaChatModel()
        response = requests.get(f"{ollama_model.base_url}/api/tags")
        if response.status_code == 200:
            models = response.json().get("models", [])
            print(f"✅ Ollama is running. Available models: {[m['name'] for m in models]}")
            return True
        else:
            print("❌ Ollama is not responding properly")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        print("Make sure Ollama is installed and running: 'ollama serve'")
        return False

if __name__ == "__main__":
    print("Testing Ollama connection...")
    if test_ollama_connection():
        print("\nRunning example...")
        asyncio.run(example_usage())
    else:
        print("\nPlease start Ollama first: 'ollama serve'")
