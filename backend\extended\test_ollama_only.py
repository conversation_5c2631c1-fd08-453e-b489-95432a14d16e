#!/usr/bin/env python3
"""
Simple test for Ollama integration without browser-use dependencies.
"""

import asyncio
import requests
from browser_use_agent import OllamaChatModel
from langchain_core.messages import HumanMessage

def test_ollama_connection():
    """Test if Ollama is running."""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            print("✅ Ollama is running")
            print(f"📦 Available models: {[m['name'] for m in models]}")
            return True
        else:
            print(f"❌ Ollama responded with status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Ollama: {e}")
        print("💡 Make sure Ollama is running: ollama serve")
        return False

def test_ollama_model():
    """Test the Ollama model directly."""
    try:
        print("\n🧪 Testing Ollama Model")

        # Try different models that might be available
        models_to_try = ["llama3.2", "llama2", "llama3.1"]

        for model_name in models_to_try:
            try:
                print(f"🔄 Trying model: {model_name}")
                model = OllamaChatModel(model=model_name)
                print(f"✅ Created model: {model.model}")

                # Test generation
                messages = [HumanMessage(content="Say 'Hello from Ollama!' and nothing else.")]
                result = model._generate(messages)
                response = result.generations[0].message.content

                print(f"📤 Sent: Say 'Hello from Ollama!' and nothing else.")
                print(f"📥 Received: {response}")

                if "Hello" in response or "Ollama" in response:
                    print(f"✅ Model {model_name} is working correctly!")
                    return True
                else:
                    print(f"⚠️  Model {model_name} responded but content seems unexpected")
                    return True

            except Exception as e:
                print(f"❌ Error with model {model_name}: {e}")
                continue

        print("❌ No working models found")
        return False

    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def main():
    """Run tests."""
    print("🚀 SmartSurf Ollama Integration Test")
    print("=" * 40)

    # Test connection
    if not test_ollama_connection():
        print("\n💡 To fix:")
        print("1. Install Ollama: https://ollama.ai/download")
        print("2. Start Ollama: ollama serve")
        print("3. Pull a model: ollama pull llama3.2")
        return

    # Test model creation (without generation for now)
    try:
        print("\n🧪 Testing Model Creation")
        model = OllamaChatModel(model="llama3.2")
        print(f"✅ Successfully created OllamaChatModel with model: {model.model}")
        print(f"📡 Base URL: {model.base_url}")
        print(f"🔧 LLM Type: {model._llm_type}")

        print("\n🎉 Ollama integration setup is complete!")
        print("\n💡 You can now use OllamaChatModel in your code:")
        print("   from browser_use_agent import OllamaChatModel")
        print("   model = OllamaChatModel(model='llama3.2')")
        print("\n⚠️  Note: Text generation may require working Ollama models.")
        print("   If you get CUDA errors, try: ollama pull llama3.2:latest")

    except Exception as e:
        print(f"❌ Error creating model: {e}")

    # Optional: Test model generation
    print("\n❓ Would you like to test text generation? (may fail if models have issues)")
    try:
        choice = input("Test generation? (y/n): ").lower().strip()
        if choice in ['y', 'yes']:
            if test_ollama_model():
                print("\n🎉 Text generation is also working!")
            else:
                print("\n⚠️  Text generation failed, but basic setup is OK")
    except (EOFError, KeyboardInterrupt):
        print("\nSkipping generation test.")

if __name__ == "__main__":
    main()
